import json
import yaml
import logging
from model.data_connectors import SQLServerDatabaseConnection
from model.optimization_input import OptimizationInput
from model.optimization_model import PurchaseOrderOptimization
from excel.excel_integration import ExcelIntegration
import pickle 
import logging.config



if __name__ == '__main__':
    try:
        # Set up DictConfig for the logger with a rotating file handler and debug level info and also log to console
        with open('./config/logging_config.json', 'r') as f:
            logging_config = json.load(f)
        logging.config.dictConfig(logging_config)

        # Load the config file
        with open('./config/config.json') as f:
            config = json.load(f)
        
        # Load the sql queries file
        with open('./config/sql_queries.yml') as f:
            sql_queries = yaml.load(f, Loader=yaml.FullLoader)

        #intialise a non root logger 
        logger_instance = logging.getLogger('main.py')
        
        # The following code executes all the queries in the sql_queries.yml file and stores the results in a dictionary
        df = {}
        for query_name in sql_queries.keys():
            query_string = sql_queries[query_name]['query']
            database_to_hit = sql_queries[query_name]['database']
            with SQLServerDatabaseConnection( **config['databases'][database_to_hit], **config['database_users']['dbuser'] ) as conn:
                result = conn.execute_query(query_string)
                df[f'{query_name}'] = result

        input_data_map = df['product_dims_and_load_qty'].loc[:, ['desc', 'itemid', 'factory', 'planner'] ]
        pickle.dump(input_data_map, open('./input_data_map.pkl', 'wb'))
        
        #Getting the items selected from the PO_Optimization.xlsm spreadsheet
        selected_items= ExcelIntegration.get_selections('./PO_Optimization.xlsm')
        logger_instance.info('Selected Items:', selected_items)

        # Setting up inputs for the optimization model 
        # Setting up forecast items as input. Needs to be a unique list of item ids from product_dims_and_load_qty filtered by proudct desc containing a keyword.
        forecast_items = ( df['product_dims_and_load_qty']
                            .loc[df['product_dims_and_load_qty']['itemid'].isin(selected_items['itemids_selected']),['itemid']]
                            .drop_duplicates()['itemid']
                            .to_list() 
                        )
        logger_instance.info("Forecast Items")
        logger_instance.info("---------------")
        logger_instance.info(forecast_items)

        
        # Setting up forecast data as input contianing only the forecast items
        forecast_data =( df['forecast_data'].loc[df['forecast_data']['ItemID'].isin(forecast_items)]
                            .set_index(['ItemID', 'Week'])['Value']
                            .to_dict()
                        )
        
        # All items in the forecast_items should have forecast value. If not set it to 0 for all weeks.
        for item in forecast_items:
            for week in df['weeks_index']['Week'].tolist():
                if (item, week) not in forecast_data:
                    forecast_data[(item, week)] = 0
                    print(f'Item {item} does not have a forecast for week {week}. Setting it to 0')

        forecast_periods = df['weeks_index']['Week'].tolist()
        variable_periods = forecast_periods[8:]
        

        logger_instance.info("Forecast Data for the selected items for optimization")
        logger_instance.info("-----------------------------------------------------")
        logger_instance.info("Forecast Periods")
        logger_instance.info(forecast_periods)
        logger_instance.info("Variable Periods - Forecast Periods over which we can control order placement")
        logger_instance.info("------------------------------------------------------------------------------")
        logger_instance.info(variable_periods)

        # Setting up beginning inventory as input. The ouput dictionary index should be the item id and the first week of the forecasting period.   
        beginning_inventory = ( df['curr_inv_and_gross_margins']
                                .loc[df['curr_inv_and_gross_margins']['itemid'].isin(forecast_items)]
                                .assign(start_week = lambda x: forecast_periods[0])
                                .set_index(['itemid','start_week' ])['curr_inventory']
                                .to_dict()
                            )
        logger_instance.info("Beginning Period Inventory")
        logger_instance.info("--------------------------")
        logger_instance.info(beginning_inventory)

        # Setting up arrival data as input. The ouput dictionary index should be the item id and the week.
        arrival_data = ( df['arrival_data']
                            .loc[df['arrival_data']['itemid'].isin(forecast_items) & df['arrival_data']['WeekYr'].isin(forecast_periods)]
                            .set_index(['itemid', 'WeekYr'])['total_arrival_qty']
                            .to_dict()
                        )
        # If there is no arrival data for an item in a week, then the value should be 0
        for item in forecast_items:
            for week in forecast_periods:
                if (item, week) not in arrival_data:
                    arrival_data[(item, week)] = 0
        logger_instance.info( f"Arrival Data \n ---------- \n   {arrival_data}")
        
        # Setting up gross margin data as input. The ouput dictionary index should be the item id 

        gross_margin_data = ( df['curr_inv_and_gross_margins']
                                .loc[df['curr_inv_and_gross_margins']['itemid'].isin(forecast_items)]
                                .set_index(['itemid'])['gross_margin']
                                .to_dict()
                            )
        
        logger_instance.info("Gross Margins Calculated")
        logger_instance.info("--------------------------")
        logger_instance.info(gross_margin_data)

        # Setting up storage cost per cuft as input
        storage_cost_per_cuft = 0.007
        logger_instance.info(f" Storage Cost Per Sq. Ft = $ {storage_cost_per_cuft} ")

        # Setting up storage volume as input. The ouput dictionary index should be the item id and the first week of the forecasting period. The value should be the ending inventory for that item in that week multiplied by the storage volume per unit.
        storage_volume = ( df['product_dims_and_load_qty']
                            .loc[df['product_dims_and_load_qty']['itemid'].isin(forecast_items)]
                            .set_index(['itemid'])['cuft']
                            .to_dict()
                        )
        logger_instance.info("Storage Volume Calculated off the Ending Inventory")
        logger_instance.info("--------------------------------------------------")
        logger_instance.info("storage_volume")

        # Setting up container batch size as input

        filtered_product_dims_and_load_qty = ( df['product_dims_and_load_qty']
                            .loc[df['product_dims_and_load_qty']['itemid'].isin(forecast_items)]                        
                        )
        
        
        logger_instance.info("Filtered Product Dimensions and Load Quantity")
        logger_instance.info("-----------------------------------------------")
        logger_instance.info(filtered_product_dims_and_load_qty)
        
        container_batch_size = filtered_product_dims_and_load_qty['load quantity'].mean().round(0)
        logger_instance.info("Container Batch Size - Calculated as the mean of the load quantity for the selected items")
        logger_instance.info("-----------------------------------------------")
        logger_instance.info(f"Container Batch Size = {container_batch_size}")
        
        print(container_batch_size)

        # Setting up the optimization input
        optimization_input = OptimizationInput(
            forecast_data = forecast_data,
            forecast_periods = forecast_periods,
            variable_periods = variable_periods,
            forecast_items = forecast_items,
            beginning_inventory = beginning_inventory,
            gross_margin_data = gross_margin_data,
            storage_cost_per_cuft = storage_cost_per_cuft,
            storage_volume = storage_volume,
            container_batch_size= container_batch_size,
            arrival_data = arrival_data
        )

        PO_Optimization_Model = PurchaseOrderOptimization(optimization_input)
        PO_Optimization_Model.optimize()
        results = PO_Optimization_Model.get_results()
        logger_instance.info("Optimization Results")
        logger_instance.info("---------------------")
        logger_instance.info(results)
        ExcelIntegration.populate_output('./PO_Optimization.xlsm', results)
    
    except Exception as e:
        logger_instance.error(f"Error: {e}")
        raise e
        
