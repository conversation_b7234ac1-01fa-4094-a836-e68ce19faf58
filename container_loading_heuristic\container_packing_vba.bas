Sub RunContainerPackingOptimization()
    '
    ' Container Packing Optimization with Batch Support
    ' Automatically detects and processes batches or single optimization
    '

    Dim shell As Object
    Dim workbookPath As String
    Dim command As String
    Dim pythonScript As String
    Dim batchCount As Integer

    ' Create WScript.Shell object
    Set shell = CreateObject("WScript.Shell")

    ' Get current workbook path
    workbookPath = ThisWorkbook.Path
    pythonScript = workbookPath & "\container_loading.py"

    ' Check if Python script exists
    If Dir(pythonScript) = "" Then
        MsgBox "Python script not found: " & pythonScript, vbCritical, "Error"
        Exit Sub
    End If

    ' Check for batch configuration
    batchCount = CountActiveBatches()

    ' Save workbook to avoid conflicts
    ThisWorkbook.Save

    ' Show appropriate message based on batch configuration
    If batchCount > 1 Then
        MsgBox "Starting MULTIPLE BATCH optimization..." & vbCrLf & vbCrLf & _
               "Found " & batchCount & " active batches to process." & vbCrLf & vbCrLf & _
               "The Python script will:" & vbCrLf & _
               "• Process each active batch separately" & vbCrLf & _
               "• Generate individual results for each batch" & vbCrLf & _
               "• Create a summary comparison report" & vbCrLf & _
               "• Generate batch-specific visualizations" & vbCrLf & _
               "• Keep Excel open when complete" & vbCrLf & vbCrLf & _
               "Click OK to start the process.", vbInformation, "Starting Multi-Batch Optimization"
    ElseIf batchCount = 1 Then
        MsgBox "Starting SINGLE BATCH optimization..." & vbCrLf & vbCrLf & _
               "Found 1 active batch to process." & vbCrLf & vbCrLf & _
               "The Python script will:" & vbCrLf & _
               "• Process the active batch configuration" & vbCrLf & _
               "• Apply batch-specific settings" & vbCrLf & _
               "• Update Excel with results" & vbCrLf & _
               "• Keep Excel open when complete" & vbCrLf & vbCrLf & _
               "Click OK to start the process.", vbInformation, "Starting Single Batch Optimization"
    Else
        MsgBox "Starting TRADITIONAL optimization..." & vbCrLf & vbCrLf & _
               "No active batches found - using traditional method." & vbCrLf & vbCrLf & _
               "The Python script will:" & vbCrLf & _
               "• Read data from items_to_pack sheet" & vbCrLf & _
               "• Run standard optimization" & vbCrLf & _
               "• Update Excel with results" & vbCrLf & _
               "• Keep Excel open when complete" & vbCrLf & vbCrLf & _
               "Click OK to start the process.", vbInformation, "Starting Traditional Optimization"
    End If

    ' Build command to run Python script
    command = "cmd /c cd /d """ & workbookPath & """ && python container_loading.py"

    ' Run script without waiting (to avoid Excel conflicts)
    shell.Run command, 1, False  ' 1 = normal window, False = don't wait

    ' Show completion message
    MsgBox "Optimization script has been started!" & vbCrLf & vbCrLf & _
           "The script is now running in the background." & vbCrLf & _
           "Excel will be updated automatically when complete." & vbCrLf & vbCrLf & _
           "You can continue working or close this message.", vbInformation, "Script Started"

    ' Clean up
    Set shell = Nothing

End Sub

Function CountActiveBatches() As Integer
    '
    ' Count the number of active batches in Batch_Config sheet
    '

    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim activeCount As Integer

    activeCount = 0

    ' Check if Batch_Config sheet exists
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("Batch_Config")
    On Error GoTo 0

    If ws Is Nothing Then
        CountActiveBatches = 0
        Exit Function
    End If

    ' Find last row with data
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    ' Count active batches (column D = "YES")
    For i = 2 To lastRow ' Start from row 2 to skip header
        If UCase(Trim(ws.Cells(i, 4).Value)) = "YES" Then
            activeCount = activeCount + 1
        End If
    Next i

    CountActiveBatches = activeCount

End Function

Sub RefreshResults()
    '
    ' Refresh result sheets after optimization completes
    ' Handles both traditional and batch result sheets
    '

    Dim ws As Worksheet
    Dim resultSheets As Variant
    Dim batchSheets As Collection
    Dim i As Integer
    Dim sheetsFound As Integer
    Dim sheetName As String

    ' Traditional result sheets to refresh
    resultSheets = Array("Packing_Results", "Summary_Statistics", "Item_Counts", "Batch_Summary")
    sheetsFound = 0

    ' Find batch-specific sheets
    Set batchSheets = New Collection
    For Each ws In ThisWorkbook.Sheets
        If InStr(ws.Name, "Packing_Results_BATCH_") > 0 Or _
           InStr(ws.Name, "Summary_BATCH_") > 0 Or _
           InStr(ws.Name, "Item_Counts_BATCH_") > 0 Then
            batchSheets.Add ws.Name
        End If
    Next ws

    Application.ScreenUpdating = False

    ' Refresh traditional result sheets
    For i = 0 To UBound(resultSheets)
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(resultSheets(i))
        If Not ws Is Nothing Then
            ws.Calculate
            ws.Columns.AutoFit
            ws.Rows.AutoFit
            sheetsFound = sheetsFound + 1
        End If
        Set ws = Nothing
        On Error GoTo 0
    Next i

    ' Refresh batch-specific sheets
    For i = 1 To batchSheets.Count
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(batchSheets(i))
        If Not ws Is Nothing Then
            ws.Calculate
            ws.Columns.AutoFit
            ws.Rows.AutoFit
            sheetsFound = sheetsFound + 1
        End If
        Set ws = Nothing
        On Error GoTo 0
    Next i

    Application.ScreenUpdating = True

    If sheetsFound > 0 Then
        If batchSheets.Count > 0 Then
            MsgBox "Refreshed " & sheetsFound & " result sheets successfully!" & vbCrLf & _
                   "Including " & batchSheets.Count & " batch-specific sheets.", vbInformation, "Results Refreshed"
        Else
            MsgBox "Refreshed " & sheetsFound & " result sheets successfully!", vbInformation, "Results Refreshed"
        End If
    Else
        MsgBox "No result sheets found. Run the optimization first.", vbExclamation, "No Results"
    End If

End Sub

Sub OpenVisualizations()
    '
    ' Open generated HTML visualization files
    ' Handles both traditional and batch-specific visualizations
    '

    Dim shell As Object
    Dim workbookPath As String
    Dim fileName As String
    Dim filesOpened As Integer
    Dim fileList As String

    Set shell = CreateObject("WScript.Shell")
    workbookPath = ThisWorkbook.Path
    filesOpened = 0
    fileList = ""

    ' Open traditional 3D visualization
    fileName = workbookPath & "\container_packing_3d_visualization.html"
    If Dir(fileName) <> "" Then
        shell.Run """" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 3D Visualization (Traditional)" & vbCrLf
    End If

    ' Open traditional 2D visualization
    fileName = workbookPath & "\container_packing_2d_views.html"
    If Dir(fileName) <> "" Then
        shell.Run """" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 2D Views (Traditional)" & vbCrLf
    End If

    ' Open batch-specific visualizations
    fileName = Dir(workbookPath & "\container_packing_3d_BATCH_*.html")
    Do While fileName <> ""
        shell.Run """" & workbookPath & "\" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 3D Visualization (" & Mid(fileName, 24, Len(fileName) - 28) & ")" & vbCrLf
        fileName = Dir
    Loop

    fileName = Dir(workbookPath & "\container_packing_2d_BATCH_*.html")
    Do While fileName <> ""
        shell.Run """" & workbookPath & "\" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 2D Views (" & Mid(fileName, 23, Len(fileName) - 27) & ")" & vbCrLf
        fileName = Dir
    Loop

    Set shell = Nothing

    ' Show summary of opened files
    If filesOpened > 0 Then
        MsgBox "Opened " & filesOpened & " visualization file(s):" & vbCrLf & vbCrLf & fileList, vbInformation, "Visualizations Opened"
    Else
        MsgBox "No visualization files found." & vbCrLf & vbCrLf & _
               "Run the optimization first to generate visualizations.", vbExclamation, "No Visualizations"
    End If

End Sub

Sub SetupBatchConfiguration()
    '
    ' Helper subroutine to setup initial batch configuration
    ' Creates the batch structure if it doesn't exist
    '

    Dim ws As Worksheet
    Dim batchConfigExists As Boolean
    Dim batchItemsExists As Boolean

    ' Check if batch sheets exist
    batchConfigExists = False
    batchItemsExists = False

    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "Batch_Config" Then batchConfigExists = True
        If ws.Name = "Batch_Items" Then batchItemsExists = True
    Next ws

    If batchConfigExists And batchItemsExists Then
        MsgBox "Batch configuration already exists!" & vbCrLf & vbCrLf & _
               "You can modify the existing Batch_Config and Batch_Items sheets.", vbInformation, "Batch Setup"
    Else
        MsgBox "Batch configuration will be created when you run the optimization." & vbCrLf & vbCrLf & _
               "The system will automatically create:" & vbCrLf & _
               "• Batch_Config sheet (batch settings)" & vbCrLf & _
               "• Batch_Items sheet (items per batch)" & vbCrLf & vbCrLf & _
               "Run the optimization to create the batch structure.", vbInformation, "Batch Setup"
    End If

End Sub
