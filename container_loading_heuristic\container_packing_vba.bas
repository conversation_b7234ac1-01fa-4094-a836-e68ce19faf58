Sub RunContainerPackingOptimization()
    '
    ' Container Packing Optimization with Batch Support
    ' Automatically detects and processes batches or single optimization
    '

    Dim shell As Object
    Dim workbookPath As String
    Dim command As String
    Dim pythonScript As String
    Dim batchCount As Integer

    ' Create WScript.Shell object
    Set shell = CreateObject("WScript.Shell")

    ' Get current workbook path
    workbookPath = ThisWorkbook.Path
    pythonScript = workbookPath & "\container_loading.py"

    ' Check if Python script exists
    If Dir(pythonScript) = "" Then
        MsgBox "Python script not found: " & pythonScript, vbCritical, "Error"
        Exit Sub
    End If

    ' Check for item configuration
    batchCount = CountActiveItems()

    ' Save workbook to avoid conflicts
    ThisWorkbook.Save

    ' Show appropriate message based on item configuration
    If batchCount > 1 Then
        MsgBox "Starting MULTIPLE ITEM optimization..." & vbCrLf & vbCrLf & _
               "Found " & batchCount & " active items to process." & vbCrLf & vbCrLf & _
               "Each item contains multiple boxes with varying dimensions." & vbCrLf & vbCrLf & _
               "The Python script will:" & vbCrLf & _
               "• Process each active item separately" & vbCrLf & _
               "• Pack all boxes for each item optimally" & vbCrLf & _
               "• Generate individual results for each item" & vbCrLf & _
               "• Create a summary comparison report" & vbCrLf & _
               "• Generate item-specific visualizations" & vbCrLf & _
               "• Keep Excel open when complete" & vbCrLf & vbCrLf & _
               "Click OK to start the process.", vbInformation, "Starting Multi-Item Optimization"
    ElseIf batchCount = 1 Then
        MsgBox "Starting SINGLE ITEM optimization..." & vbCrLf & vbCrLf & _
               "Found 1 active item to process." & vbCrLf & vbCrLf & _
               "This item contains multiple boxes with varying dimensions." & vbCrLf & vbCrLf & _
               "The Python script will:" & vbCrLf & _
               "• Process the active item configuration" & vbCrLf & _
               "• Pack all boxes for this item optimally" & vbCrLf & _
               "• Apply item-specific settings (equal box counts, etc.)" & vbCrLf & _
               "• Update Excel with results" & vbCrLf & _
               "• Keep Excel open when complete" & vbCrLf & vbCrLf & _
               "Click OK to start the process.", vbInformation, "Starting Single Item Optimization"
    Else
        MsgBox "Starting TRADITIONAL optimization..." & vbCrLf & vbCrLf & _
               "No active items found - using traditional method." & vbCrLf & vbCrLf & _
               "The Python script will:" & vbCrLf & _
               "• Read data from items_to_pack sheet" & vbCrLf & _
               "• Run standard optimization" & vbCrLf & _
               "• Update Excel with results" & vbCrLf & _
               "• Keep Excel open when complete" & vbCrLf & vbCrLf & _
               "Click OK to start the process.", vbInformation, "Starting Traditional Optimization"
    End If

    ' Build command to run Python script
    command = "cmd /c cd /d """ & workbookPath & """ && python container_loading.py"

    ' Run script without waiting (to avoid Excel conflicts)
    shell.Run command, 1, False  ' 1 = normal window, False = don't wait

    ' Show completion message
    MsgBox "Optimization script has been started!" & vbCrLf & vbCrLf & _
           "The script is now running in the background." & vbCrLf & _
           "Excel will be updated automatically when complete." & vbCrLf & vbCrLf & _
           "You can continue working or close this message.", vbInformation, "Script Started"

    ' Clean up
    Set shell = Nothing

End Sub

Function CountActiveItems() As Integer
    '
    ' Count the number of active items in Item_Batches sheet
    '

    Dim ws As Worksheet
    Dim lastRow As Long
    Dim i As Long
    Dim activeCount As Integer

    activeCount = 0

    ' Check if Item_Batches sheet exists
    On Error Resume Next
    Set ws = ThisWorkbook.Sheets("Item_Batches")
    On Error GoTo 0

    If ws Is Nothing Then
        CountActiveItems = 0
        Exit Function
    End If

    ' Find last row with data
    lastRow = ws.Cells(ws.Rows.Count, 1).End(xlUp).Row

    ' Count active items (column C = "YES")
    For i = 2 To lastRow ' Start from row 2 to skip header
        If UCase(Trim(ws.Cells(i, 3).Value)) = "YES" Then
            activeCount = activeCount + 1
        End If
    Next i

    CountActiveItems = activeCount

End Function

Sub RefreshResults()
    '
    ' Refresh result sheets after optimization completes
    ' Handles both traditional and batch result sheets
    '

    Dim ws As Worksheet
    Dim resultSheets As Variant
    Dim batchSheets As Collection
    Dim i As Integer
    Dim sheetsFound As Integer
    Dim sheetName As String

    ' Traditional result sheets to refresh
    resultSheets = Array("Packing_Results", "Summary_Statistics", "Item_Counts", "Item_Summary")
    sheetsFound = 0

    ' Find item-specific sheets
    Set batchSheets = New Collection
    For Each ws In ThisWorkbook.Sheets
        If InStr(ws.Name, "Packing_Results_ITEM_") > 0 Or _
           InStr(ws.Name, "Summary_ITEM_") > 0 Or _
           InStr(ws.Name, "Box_Counts_ITEM_") > 0 Then
            batchSheets.Add ws.Name
        End If
    Next ws

    Application.ScreenUpdating = False

    ' Refresh traditional result sheets
    For i = 0 To UBound(resultSheets)
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(resultSheets(i))
        If Not ws Is Nothing Then
            ws.Calculate
            ws.Columns.AutoFit
            ws.Rows.AutoFit
            sheetsFound = sheetsFound + 1
        End If
        Set ws = Nothing
        On Error GoTo 0
    Next i

    ' Refresh batch-specific sheets
    For i = 1 To batchSheets.Count
        On Error Resume Next
        Set ws = ThisWorkbook.Sheets(batchSheets(i))
        If Not ws Is Nothing Then
            ws.Calculate
            ws.Columns.AutoFit
            ws.Rows.AutoFit
            sheetsFound = sheetsFound + 1
        End If
        Set ws = Nothing
        On Error GoTo 0
    Next i

    Application.ScreenUpdating = True

    If sheetsFound > 0 Then
        If batchSheets.Count > 0 Then
            MsgBox "Refreshed " & sheetsFound & " result sheets successfully!" & vbCrLf & _
                   "Including " & batchSheets.Count & " item-specific sheets.", vbInformation, "Results Refreshed"
        Else
            MsgBox "Refreshed " & sheetsFound & " result sheets successfully!", vbInformation, "Results Refreshed"
        End If
    Else
        MsgBox "No result sheets found. Run the optimization first.", vbExclamation, "No Results"
    End If

End Sub

Sub OpenVisualizations()
    '
    ' Open generated HTML visualization files
    ' Handles both traditional and batch-specific visualizations
    '

    Dim shell As Object
    Dim workbookPath As String
    Dim containerPath As String
    Dim fileName As String
    Dim filesOpened As Integer
    Dim fileList As String

    Set shell = CreateObject("WScript.Shell")
    workbookPath = ThisWorkbook.Path
    containerPath = workbookPath & "\container_loading_heuristic"
    filesOpened = 0
    fileList = ""

    ' Open traditional 3D visualization (check container folder first)
    fileName = containerPath & "\container_packing_3d_visualization.html"
    If Dir(fileName) <> "" Then
        shell.Run """" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 3D Visualization (Traditional)" & vbCrLf
    Else
        ' Check old location for backward compatibility
        fileName = workbookPath & "\container_packing_3d_visualization.html"
        If Dir(fileName) <> "" Then
            shell.Run """" & fileName & """", 1
            filesOpened = filesOpened + 1
            fileList = fileList & "• 3D Visualization (Traditional - Old Location)" & vbCrLf
        End If
    End If

    ' Open traditional 2D visualization (check container folder first)
    fileName = containerPath & "\container_packing_2d_views.html"
    If Dir(fileName) <> "" Then
        shell.Run """" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 2D Views (Traditional)" & vbCrLf
    Else
        ' Check old location for backward compatibility
        fileName = workbookPath & "\container_packing_2d_views.html"
        If Dir(fileName) <> "" Then
            shell.Run """" & fileName & """", 1
            filesOpened = filesOpened + 1
            fileList = fileList & "• 2D Views (Traditional - Old Location)" & vbCrLf
        End If
    End If

    ' Open item-specific visualizations (check container folder first)
    fileName = Dir(containerPath & "\container_packing_3d_ITEM_*.html")
    Do While fileName <> ""
        shell.Run """" & containerPath & "\" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 3D Visualization (" & Mid(fileName, 24, Len(fileName) - 28) & ")" & vbCrLf
        fileName = Dir
    Loop

    fileName = Dir(containerPath & "\container_packing_2d_ITEM_*.html")
    Do While fileName <> ""
        shell.Run """" & containerPath & "\" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 2D Views (" & Mid(fileName, 23, Len(fileName) - 27) & ")" & vbCrLf
        fileName = Dir
    Loop

    ' Also check root folder for backward compatibility
    fileName = Dir(workbookPath & "\container_packing_3d_ITEM_*.html")
    Do While fileName <> ""
        shell.Run """" & workbookPath & "\" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 3D Visualization (" & Mid(fileName, 24, Len(fileName) - 28) & " - Old Location)" & vbCrLf
        fileName = Dir
    Loop

    fileName = Dir(workbookPath & "\container_packing_2d_ITEM_*.html")
    Do While fileName <> ""
        shell.Run """" & workbookPath & "\" & fileName & """", 1
        filesOpened = filesOpened + 1
        fileList = fileList & "• 2D Views (" & Mid(fileName, 23, Len(fileName) - 27) & " - Old Location)" & vbCrLf
        fileName = Dir
    Loop

    Set shell = Nothing

    ' Show summary of opened files
    If filesOpened > 0 Then
        MsgBox "Opened " & filesOpened & " visualization file(s):" & vbCrLf & vbCrLf & fileList, vbInformation, "Visualizations Opened"
    Else
        MsgBox "No visualization files found." & vbCrLf & vbCrLf & _
               "Run the optimization first to generate visualizations." & vbCrLf & vbCrLf & _
               "Files should be in: " & containerPath, vbExclamation, "No Visualizations"
    End If

End Sub

Sub SetupItemConfiguration()
    '
    ' Helper subroutine to setup initial item configuration
    ' Creates the item structure if it doesn't exist and cleans up obsolete sheets
    '

    Dim ws As Worksheet
    Dim itemBatchesExists As Boolean
    Dim itemBoxesExists As Boolean
    Dim obsoleteSheets As Collection
    Dim sheetName As String
    Dim i As Integer

    ' Check if item sheets exist
    itemBatchesExists = False
    itemBoxesExists = False
    Set obsoleteSheets = New Collection

    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "Item_Batches" Then itemBatchesExists = True
        If ws.Name = "Item_Boxes" Then itemBoxesExists = True

        ' Identify obsolete batch-related sheets
        If ws.Name = "Batch_Config" Or ws.Name = "Batch_Items" Or ws.Name = "Batch_Summary" Or _
           InStr(ws.Name, "Packing_Results_BATCH_") > 0 Or _
           InStr(ws.Name, "Summary_BATCH_") > 0 Or _
           InStr(ws.Name, "Item_Counts_BATCH_") > 0 Then
            obsoleteSheets.Add ws.Name
        End If
    Next ws

    ' Remove obsolete sheets if found
    If obsoleteSheets.Count > 0 Then
        Dim response As VbMsgBoxResult
        response = MsgBox("Found " & obsoleteSheets.Count & " obsolete batch-related sheets." & vbCrLf & vbCrLf & _
                         "Do you want to remove them to clean up the workbook?" & vbCrLf & vbCrLf & _
                         "This will remove old Batch_Config, Batch_Items, and related result sheets.", _
                         vbYesNo + vbQuestion, "Clean Up Obsolete Sheets")

        If response = vbYes Then
            Application.DisplayAlerts = False
            For i = 1 To obsoleteSheets.Count
                On Error Resume Next
                ThisWorkbook.Sheets(obsoleteSheets(i)).Delete
                On Error GoTo 0
            Next i
            Application.DisplayAlerts = True
            MsgBox "Removed " & obsoleteSheets.Count & " obsolete sheets successfully!", vbInformation, "Cleanup Complete"
        End If
    End If

    If itemBatchesExists And itemBoxesExists Then
        MsgBox "Item configuration already exists!" & vbCrLf & vbCrLf & _
               "You can modify the existing Item_Batches and Item_Boxes sheets." & vbCrLf & vbCrLf & _
               "Structure:" & vbCrLf & _
               "• Item_Batches: Configure each item ID" & vbCrLf & _
               "• Item_Boxes: Define multiple boxes per item", vbInformation, "Item Setup"
    Else
        MsgBox "Item configuration will be created when you run the optimization." & vbCrLf & vbCrLf & _
               "The system will automatically create:" & vbCrLf & _
               "• Item_Batches sheet (item settings)" & vbCrLf & _
               "• Item_Boxes sheet (boxes per item)" & vbCrLf & vbCrLf & _
               "Each item can have multiple boxes with varying dimensions." & vbCrLf & vbCrLf & _
               "Run the optimization to create the item structure.", vbInformation, "Item Setup"
    End If

End Sub

Sub CleanupObsoleteSheets()
    '
    ' Remove all obsolete batch-related sheets
    '

    Dim ws As Worksheet
    Dim obsoleteSheets As Collection
    Dim i As Integer

    Set obsoleteSheets = New Collection

    ' Find all obsolete sheets
    For Each ws In ThisWorkbook.Sheets
        If ws.Name = "Batch_Config" Or ws.Name = "Batch_Items" Or ws.Name = "Batch_Summary" Or _
           InStr(ws.Name, "Packing_Results_BATCH_") > 0 Or _
           InStr(ws.Name, "Summary_BATCH_") > 0 Or _
           InStr(ws.Name, "Item_Counts_BATCH_") > 0 Then
            obsoleteSheets.Add ws.Name
        End If
    Next ws

    If obsoleteSheets.Count = 0 Then
        MsgBox "No obsolete sheets found to clean up.", vbInformation, "Cleanup Complete"
        Exit Sub
    End If

    Dim response As VbMsgBoxResult
    response = MsgBox("Found " & obsoleteSheets.Count & " obsolete batch-related sheets:" & vbCrLf & vbCrLf & _
                     "Do you want to remove them?" & vbCrLf & vbCrLf & _
                     "This action cannot be undone.", vbYesNo + vbExclamation, "Confirm Cleanup")

    If response = vbYes Then
        Application.DisplayAlerts = False
        For i = 1 To obsoleteSheets.Count
            On Error Resume Next
            ThisWorkbook.Sheets(obsoleteSheets(i)).Delete
            On Error GoTo 0
        Next i
        Application.DisplayAlerts = True

        ' Save the workbook
        ThisWorkbook.Save

        MsgBox "Successfully removed " & obsoleteSheets.Count & " obsolete sheets!" & vbCrLf & vbCrLf & _
               "The workbook has been cleaned up and saved.", vbInformation, "Cleanup Complete"
    End If

End Sub
