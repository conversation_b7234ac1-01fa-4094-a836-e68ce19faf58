import xlwings as xw
from py3dbp import Packer, Bin, Item
from datetime import datetime
import plotly.graph_objects as go
import plotly.express as px
import numpy as np
import warnings
warnings.filterwarnings('ignore')

class ContainerPackingOptimizer:
    def __init__(self, excel_file_path, enable_3d_wireframe=True, enable_2d_plots=True):
        """
        Initialize the container packing optimizer.

        Args:
            excel_file_path (str): Path to the Excel file containing items to pack
            enable_3d_wireframe (bool): Whether to generate 3D wireframe visualization
            enable_2d_plots (bool): Whether to generate 2D cross-section plots
        """
        self.excel_file_path = excel_file_path
        self.items_df = None
        self.packer = None
        self.container = None
        self.packed_items = []
        self.packing_results = {}

        # Visualization settings
        self.enable_3d_wireframe = enable_3d_wireframe
        self.enable_2d_plots = enable_2d_plots

        # 40HQ Container specifications (in meters)
        self.container_specs = {
            'length': 12.032,  # 40 feet
            'width': 2.352,    # 7.7 feet
            'height': 2.698,   # 8.85 feet
            'max_weight': 20411,  # kg (45,000 lbs converted to kg)
            'volume': 12.032 * 2.352 * 2.698  # cubic meters
        }

    def setup_batch_structure(self):
        """Setup the Excel structure for item-based batches and remove obsolete sheets."""
        try:
            wb = xw.Book(self.excel_file_path)

            # Remove obsolete batch-related sheets
            obsolete_sheets = ['Batch_Config', 'Batch_Items', 'Batch_Summary']
            for sheet_name in obsolete_sheets:
                try:
                    ws = wb.sheets[sheet_name]
                    ws.delete()
                    print(f"Removed obsolete sheet: {sheet_name}")
                except:
                    pass  # Sheet doesn't exist, which is fine

            # Check if Item_Batches sheet exists and has data
            item_batches_exists = False
            item_batches_has_data = False
            try:
                item_batches_ws = wb.sheets['Item_Batches']
                item_batches_exists = True
                # Check if it has data (more than just headers)
                data = item_batches_ws.used_range.value
                if data and len(data) > 1:
                    item_batches_has_data = True
                    print("Item_Batches sheet exists with data - preserving existing configuration")
            except:
                pass

            # Only create/populate Item_Batches if it doesn't exist or has no data
            if not item_batches_exists:
                item_batches_ws = wb.sheets.add('Item_Batches')
                print("Created new Item_Batches sheet")

            if not item_batches_has_data:
                print("Populating Item_Batches with default data")
                # Setup Item_Batches sheet - each batch represents one item ID
                item_batches_data = [
                    ['Item_ID', 'Item_Description', 'Active', 'Equal_Box_Counts', 'Enable_3D_Wireframe', 'Enable_2D_Plots'],
                    ['ITEM_001', 'Odelia Swivel Glider Rocker Recliner - Light Gray', 'YES', 'YES', 'YES', 'YES'],
                    ['ITEM_002', 'Office Chair - Executive Series', 'NO', 'YES', 'NO', 'NO'],
                    ['ITEM_003', 'Dining Table Set - 6 Piece', 'NO', 'NO', 'YES', 'YES']
                ]
                item_batches_ws.range('A1').value = item_batches_data
                item_batches_ws.range('A1').expand('right').api.Font.Bold = True
                item_batches_ws.autofit()

            # Check if Item_Boxes sheet exists and has data
            item_boxes_exists = False
            item_boxes_has_data = False
            try:
                item_boxes_ws = wb.sheets['Item_Boxes']
                item_boxes_exists = True
                # Check if it has data (more than just headers)
                data = item_boxes_ws.used_range.value
                if data and len(data) > 1:
                    item_boxes_has_data = True
                    print("Item_Boxes sheet exists with data - preserving existing configuration")
            except:
                pass

            # Only create/populate Item_Boxes if it doesn't exist or has no data
            if not item_boxes_exists:
                item_boxes_ws = wb.sheets.add('Item_Boxes')
                print("Created new Item_Boxes sheet")

            if not item_boxes_has_data:
                print("Populating Item_Boxes with default data")
                # Setup Item_Boxes sheet - multiple boxes per item ID
                item_boxes_data = [
                    ['Item_ID', 'Box_Number', 'Box_Description', 'Length_inches', 'Width_inches', 'Height_inches', 'Weight_lbs'],
                    ['ITEM_001', 1, 'Main Chair Frame', 31.1, 29.92, 25.6, 85.0],
                    ['ITEM_001', 2, 'Chair Base and Hardware', 18.5, 15.2, 8.3, 15.0],
                    ['ITEM_002', 1, 'Chair Seat and Back', 28.0, 26.5, 22.0, 45.0],
                    ['ITEM_002', 2, 'Chair Base Assembly', 20.0, 20.0, 12.0, 25.0],
                    ['ITEM_002', 3, 'Hardware and Accessories', 12.0, 8.0, 4.0, 5.0],
                    ['ITEM_003', 1, 'Table Top', 48.0, 30.0, 4.0, 60.0],
                    ['ITEM_003', 2, 'Table Legs and Support', 32.0, 8.0, 6.0, 25.0],
                    ['ITEM_003', 3, 'Chair Set (4 chairs)', 24.0, 20.0, 18.0, 40.0],
                    ['ITEM_003', 4, 'Hardware and Assembly Kit', 16.0, 12.0, 6.0, 8.0]
                ]
                item_boxes_ws.range('A1').value = item_boxes_data
                item_boxes_ws.range('A1').expand('right').api.Font.Bold = True
                item_boxes_ws.autofit()

            # Auto-sync Item_Batches with Item_Boxes
            self.auto_sync_item_batches(wb)

            wb.save()
            print("Item-based structure setup completed")
            print("Structure:")
            print("  - Item_Batches: Configuration for each item ID")
            print("  - Item_Boxes: Multiple boxes per item ID with varying dimensions")
            if item_batches_has_data or item_boxes_has_data:
                print("Existing data preserved")
            print("Auto-sync: Item_Batches updated based on Item_Boxes")
            return True

        except Exception as e:
            print(f"Error setting up item structure: {e}")
            return False

    def auto_sync_item_batches(self, wb):
        """Auto-populate Item_Batches based on unique Item_IDs found in Item_Boxes."""
        try:
            # Get Item_Boxes sheet
            try:
                item_boxes_ws = wb.sheets['Item_Boxes']
                boxes_data = item_boxes_ws.used_range.value
            except:
                print("Item_Boxes sheet not found - skipping auto-sync")
                return False

            # Get Item_Batches sheet
            try:
                item_batches_ws = wb.sheets['Item_Batches']
                batches_data = item_batches_ws.used_range.value
            except:
                print("Item_Batches sheet not found - skipping auto-sync")
                return False

            if not boxes_data or len(boxes_data) < 2:
                print("No data in Item_Boxes - skipping auto-sync")
                return False

            # Extract unique Item_IDs from Item_Boxes
            unique_item_ids = set()
            for row in boxes_data[1:]:  # Skip header
                if row and row[0]:  # Check if Item_ID exists
                    unique_item_ids.add(row[0])

            # Extract existing Item_IDs from Item_Batches
            existing_item_ids = set()
            if batches_data and len(batches_data) > 1:
                for row in batches_data[1:]:  # Skip header
                    if row and row[0]:  # Check if Item_ID exists
                        existing_item_ids.add(row[0])

            # Find missing Item_IDs
            missing_item_ids = unique_item_ids - existing_item_ids

            if not missing_item_ids:
                print(f"Auto-sync: All {len(unique_item_ids)} items already exist in Item_Batches")
                return True

            print(f"Auto-sync: Found {len(missing_item_ids)} new items to add: {sorted(missing_item_ids)}")

            # Prepare new rows to add
            new_rows = []
            for item_id in sorted(missing_item_ids):
                # Generate a default description
                item_description = f"Auto-detected Item {item_id}"

                # Default settings: Active=NO (user must manually activate), Equal_Box_Counts=YES, Visualizations=YES
                new_row = [item_id, item_description, 'NO', 'YES', 'YES', 'YES']
                new_rows.append(new_row)

            # Add new rows to Item_Batches
            if batches_data:
                # Append to existing data
                current_data = list(batches_data)
                current_data.extend(new_rows)
            else:
                # Create new data with header
                current_data = [
                    ['Item_ID', 'Item_Description', 'Active', 'Equal_Box_Counts', 'Enable_3D_Wireframe', 'Enable_2D_Plots']
                ]
                current_data.extend(new_rows)

            # Write updated data back to sheet
            item_batches_ws.clear()
            item_batches_ws.range('A1').value = current_data
            item_batches_ws.range('A1').expand('right').api.Font.Bold = True
            item_batches_ws.autofit()

            print(f"Auto-sync: Added {len(new_rows)} new items to Item_Batches (Active=NO by default)")
            return True

        except Exception as e:
            print(f"Error during auto-sync: {e}")
            return False

    def cleanup_obsolete_sheets(self):
        """Remove all old result sheets from previous runs."""
        try:
            if hasattr(self, 'workbook') and self.workbook:
                wb = self.workbook
            else:
                wb = xw.Book(self.excel_file_path)

            # Find and remove all result sheets from previous runs
            sheets_to_remove = []
            for sheet in wb.sheets:
                sheet_name = sheet.name
                # Check if sheet matches any result pattern
                should_remove = False

                # Old batch-related sheets
                if (sheet_name.startswith('Packing_Results_BATCH_') or
                    sheet_name.startswith('Summary_BATCH_') or
                    sheet_name.startswith('Item_Counts_BATCH_') or
                    sheet_name == 'Batch_Summary'):
                    should_remove = True

                # Current item-related sheets
                elif (sheet_name.startswith('Packing_Results_ITEM_') or
                      sheet_name.startswith('Summary_ITEM_') or
                      sheet_name.startswith('Box_Counts_ITEM_')):
                    should_remove = True

                # General result sheets
                elif sheet_name in ['Packing_Results', 'Summary_Statistics', 'Item_Summary']:
                    should_remove = True

                if should_remove:
                    sheets_to_remove.append(sheet_name)

            for sheet_name in sheets_to_remove:
                try:
                    wb.sheets[sheet_name].delete()
                    print(f"Removed old result sheet: {sheet_name}")
                except:
                    pass

            if sheets_to_remove:
                wb.save()
                print(f"Cleaned up {len(sheets_to_remove)} old result sheets")
            else:
                print("No old result sheets found to clean up")

            return True

        except Exception as e:
            print(f"Error cleaning up result sheets: {e}")
            return False

    def read_items_from_excel(self, item_id=None):
        """Read item data from the Excel file using xlwings."""
        try:
            # Try to open the Excel file with xlwings
            wb = xw.Book(self.excel_file_path)
            app = wb.app
            print("Connected to existing Excel instance")

            if item_id:
                # Read from Item_Boxes sheet for specific item ID
                return self._read_item_boxes(wb, app, item_id)
            else:
                # Read from traditional items_to_pack sheet
                return self._read_traditional_items(wb, app)

        except FileNotFoundError:
            print(f"Error: Excel file not found at {self.excel_file_path}")
            return False
        except Exception as e:
            print(f"Error reading Excel file: {e}")
            return False

    def _read_item_boxes(self, wb, app, item_id):
        """Read boxes for a specific item ID."""
        try:
            # Read item configuration
            item_batches_ws = wb.sheets['Item_Batches']
            config_data = item_batches_ws.used_range.value

            item_config = None
            for row in config_data[1:]:  # Skip header
                if row and row[0] == item_id:
                    item_config = {
                        'item_id': row[0],
                        'item_description': row[1],
                        'active': row[2],
                        'equal_box_counts': row[3] == 'YES',
                        'enable_3d_wireframe': row[4] == 'YES',
                        'enable_2d_plots': row[5] == 'YES'
                    }
                    break

            if not item_config:
                print(f"Item {item_id} not found in configuration")
                return False

            if item_config['active'] != 'YES':
                print(f"Item {item_id} is not active")
                return False

            # Update visualization settings based on item config
            self.enable_3d_wireframe = item_config['enable_3d_wireframe']
            self.enable_2d_plots = item_config['enable_2d_plots']

            # Read item boxes
            item_boxes_ws = wb.sheets['Item_Boxes']
            boxes_data = item_boxes_ws.used_range.value

            headers = boxes_data[0]
            self.items_data = []

            for row in boxes_data[1:]:
                if row and row[0] == item_id:  # Match item_id
                    box_dict = {
                        'name': f"{item_config['item_description']} - Box {row[1]} ({row[2]})",
                        'length': row[3],  # Length in inches
                        'width': row[4],   # Width in inches
                        'height': row[5],  # Height in inches
                        'weight': row[6],  # Weight in lbs
                        'box_number': row[1],
                        'box_description': row[2]
                    }
                    self.items_data.append(box_dict)

            print(f"Successfully read {len(self.items_data)} box types for item {item_id}")
            print(f"Item: {item_config['item_description']}")
            print(f"Equal box counts: {item_config['equal_box_counts']}")
            print(f"3D wireframe: {item_config['enable_3d_wireframe']}")
            print(f"2D plots: {item_config['enable_2d_plots']}")

            # Print box details
            for i, box in enumerate(self.items_data):
                print(f"  Box {box['box_number']}: {box['box_description']} - {box['length']}\"×{box['width']}\"×{box['height']}\" ({box['weight']} lbs)")

            # Store item configuration
            self.current_item = item_config

            # Keep workbook and app references for later use
            self.workbook = wb
            self.excel_app = app
            return True

        except Exception as e:
            print(f"Error reading item boxes: {e}")
            return False

    def _read_traditional_items(self, wb, app):
        """Read items from traditional items_to_pack sheet."""
        try:
            # Get the items_to_pack sheet
            try:
                ws = wb.sheets['items_to_pack']
            except:
                ws = wb.sheets[0]  # Use first sheet if 'items_to_pack' doesn't exist

            # Read the data from the sheet
            data = ws.used_range.value

            if not data or len(data) < 2:
                print("Error: No data found in the Excel sheet")
                return False

            # Convert to list of dictionaries
            headers = data[0]
            self.items_data = []

            for row in data[1:]:
                if row and any(cell is not None for cell in row):  # Skip empty rows
                    item_dict = {}
                    for i, header in enumerate(headers):
                        if i < len(row):
                            item_dict[header] = row[i]
                        else:
                            item_dict[header] = None
                    self.items_data.append(item_dict)

            print(f"Successfully read {len(self.items_data)} item types from Excel file")
            print(f"Columns: {headers}")

            # Keep workbook and app references for later use
            self.workbook = wb
            self.excel_app = app
            return True

        except Exception as e:
            print(f"Error reading traditional items: {e}")
            return False

    def validate_items(self):
        """Validate item dimensions and weights."""
        valid_items = []

        for index, row in enumerate(self.items_data):
            try:
                name = str(row['name'])
                length = float(row['length'])
                width = float(row['width'])
                height = float(row['height'])
                weight = float(row['weight'])

                # Convert inches to meters if needed (assuming input is in inches)
                length_m = length * 0.0254  # inches to meters
                width_m = width * 0.0254
                height_m = height * 0.0254
                weight_kg = weight * 0.453592  # pounds to kg (assuming weight is in lbs)

                # Validate dimensions
                if length_m <= 0 or width_m <= 0 or height_m <= 0 or weight_kg <= 0:
                    print(f"Warning: Skipping item '{name}' due to invalid dimensions or weight")
                    continue

                # Check if item fits in container
                if (length_m > self.container_specs['length'] or
                    width_m > self.container_specs['width'] or
                    height_m > self.container_specs['height']):
                    print(f"Warning: Item '{name}' is too large for container")
                    continue

                valid_items.append({
                    'name': name,
                    'length_m': length_m,
                    'width_m': width_m,
                    'height_m': height_m,
                    'weight_kg': weight_kg,
                    'volume_m3': length_m * width_m * height_m,
                    'original_length': length,
                    'original_width': width,
                    'original_height': height,
                    'original_weight': weight
                })

            except (ValueError, KeyError) as e:
                print(f"Error processing row {index}: {e}")
                continue

        print(f"Validated {len(valid_items)} items out of {len(self.items_data)}")
        return valid_items

    def optimize_packing(self, max_instances_per_item=1000, equal_item_counts=True):
        """
        Optimize the packing using py3dbp.

        Args:
            max_instances_per_item (int): Maximum instances of each item type to try
            equal_item_counts (bool): Whether to ensure equal counts of all item types
        """
        valid_items = self.validate_items()
        if not valid_items:
            print("No valid items to pack")
            return False

        # Initialize packer
        self.packer = Packer()

        # Create container (bin)
        self.container = Bin(
            name="40HQ_Container",
            width=self.container_specs['width'],
            height=self.container_specs['height'],
            depth=self.container_specs['length'],
            max_weight=self.container_specs['max_weight']
        )
        self.packer.add_bin(self.container)

        # Check if we're processing an item with multiple boxes
        if hasattr(self, 'current_item'):
            equal_item_counts = self.current_item['equal_box_counts']
            print(f"Processing item {self.current_item['item_id']} with equal box counts: {equal_item_counts}")

        if equal_item_counts:
            return self._optimize_with_equal_counts(valid_items, max_instances_per_item)
        else:
            return self._optimize_standard(valid_items, max_instances_per_item)

    def _optimize_standard(self, valid_items, max_instances_per_item):
        """Standard optimization without equal count constraint."""
        # Add items to packer
        item_instances = {}
        total_items_added = 0

        for item_data in valid_items:
            item_name = item_data['name']
            item_instances[item_name] = 0

            # Calculate theoretical maximum instances based on volume
            item_volume = item_data['volume_m3']
            theoretical_max = int(self.container_specs['volume'] / item_volume)
            max_to_try = min(max_instances_per_item, theoretical_max * 2)  # Try 2x theoretical max

            print(f"Adding up to {max_to_try} instances of '{item_name}'")

            for i in range(max_to_try):
                item = Item(
                    name=f"{item_name}_instance_{i+1}",
                    width=item_data['width_m'],
                    height=item_data['height_m'],
                    depth=item_data['length_m'],
                    weight=item_data['weight_kg']
                )
                self.packer.add_item(item)
                item_instances[item_name] += 1
                total_items_added += 1

        print(f"Total items added to packer: {total_items_added}")

        # Pack items
        print("Starting packing optimization...")
        self.packer.pack(distribute_items=True, bigger_first=True)

        # Analyze results
        self.analyze_packing_results(valid_items, item_instances)
        return True

    def _optimize_with_equal_counts(self, valid_items, max_instances_per_item):
        """Optimization with equal count constraint for all item types."""
        print("Optimizing with equal item count constraint...")

        # Find the maximum number of equal items that can fit
        best_count = 0
        best_packer = None

        # Calculate a reasonable upper bound based on container constraints
        # Estimate maximum possible items based on volume and weight
        min_item_volume = min(item_data['volume_m3'] for item_data in valid_items)
        min_item_weight = min(item_data['weight_kg'] for item_data in valid_items)

        volume_limit = int(self.container_specs['volume'] / (min_item_volume * len(valid_items)))
        weight_limit = int(self.container_specs['max_weight'] / (min_item_weight * len(valid_items)))

        # Use the more restrictive limit, but cap at max_instances_per_item
        estimated_max = min(volume_limit, weight_limit, max_instances_per_item)

        print(f"Estimated maximum equal count: {estimated_max} (volume limit: {volume_limit}, weight limit: {weight_limit})")

        # Binary search for the optimal equal count
        low, high = 1, max(estimated_max, 100)  # Ensure we try at least 100

        while low <= high:
            mid = (low + high) // 2
            print(f"Testing {mid} items of each type...")

            # Create a test packer
            test_packer = Packer()
            test_container = Bin(
                name="40HQ_Container_Test",
                width=self.container_specs['width'],
                height=self.container_specs['height'],
                depth=self.container_specs['length'],
                max_weight=self.container_specs['max_weight']
            )
            test_packer.add_bin(test_container)

            # Add equal quantities of each item type
            total_weight = 0
            total_volume = 0

            for item_data in valid_items:
                item_name = item_data['name']
                for i in range(mid):
                    item = Item(
                        name=f"{item_name}_instance_{i+1}",
                        width=item_data['width_m'],
                        height=item_data['height_m'],
                        depth=item_data['length_m'],
                        weight=item_data['weight_kg']
                    )
                    test_packer.add_item(item)
                    total_weight += item_data['weight_kg']
                    total_volume += item_data['volume_m3']

            # Check if it exceeds container limits before packing
            if (total_weight > self.container_specs['max_weight'] or
                total_volume > self.container_specs['volume']):
                print(f"  {mid} items exceed container limits (weight: {total_weight:.1f}kg, volume: {total_volume:.1f}m³)")
                high = mid - 1
                continue

            # Try packing
            test_packer.pack(distribute_items=True, bigger_first=True)

            # Check if all items fit
            packed_count = len(test_container.items)
            expected_count = mid * len(valid_items)

            if packed_count == expected_count:
                print(f"  Successfully packed {mid} of each item type ({packed_count} total items)")
                best_count = mid
                best_packer = test_packer
                low = mid + 1
            else:
                print(f"  Could only pack {packed_count} out of {expected_count} items")
                high = mid - 1

        if best_count == 0:
            print("Could not pack any equal quantities. Falling back to standard optimization.")
            return self._optimize_standard(valid_items, max_instances_per_item)

        print(f"Optimal equal count: {best_count} of each item type")

        # Use the best packer result
        self.packer = best_packer
        self.container = best_packer.bins[0]

        # Create item_instances for analysis
        item_instances = {item_data['name']: best_count for item_data in valid_items}

        # Analyze results
        self.analyze_packing_results(valid_items, item_instances)
        return True

    def analyze_packing_results(self, valid_items, item_instances):
        """Analyze the packing results and calculate metrics."""
        # Get packed items
        packed_items = self.container.items
        unpacked_items = self.container.unfitted_items

        print(f"\n=== PACKING RESULTS ===")
        print(f"Total items packed: {len(packed_items)}")
        print(f"Total items unpacked: {len(unpacked_items)}")

        # Calculate metrics
        total_packed_volume = float(sum(item.width * item.height * item.depth for item in packed_items))
        total_packed_weight = float(sum(item.weight for item in packed_items))

        volume_utilization = (total_packed_volume / self.container_specs['volume']) * 100
        weight_utilization = (total_packed_weight / self.container_specs['max_weight']) * 100

        # Count items by type
        item_counts = {}
        for item in packed_items:
            # Extract original item name (remove _instance_X suffix)
            original_name = '_'.join(item.name.split('_')[:-2])
            item_counts[original_name] = item_counts.get(original_name, 0) + 1

        # Store results
        self.packing_results = {
            'total_items_packed': len(packed_items),
            'total_items_unpacked': len(unpacked_items),
            'volume_utilization_percent': volume_utilization,
            'weight_utilization_percent': weight_utilization,
            'total_packed_volume_m3': total_packed_volume,
            'total_packed_weight_kg': total_packed_weight,
            'container_volume_m3': self.container_specs['volume'],
            'container_max_weight_kg': self.container_specs['max_weight'],
            'item_counts': item_counts,
            'packed_items_details': []
        }

        # Create detailed packed items list
        for item in packed_items:
            original_name = '_'.join(item.name.split('_')[:-2])

            # Get rotation information
            rotation_type = item.rotation_type
            rotation_description = self._get_rotation_description(rotation_type)

            # Get actual dimensions after rotation
            if hasattr(item, 'get_dimension'):
                rotated_dims = item.get_dimension()
                actual_width = float(rotated_dims[0])
                actual_height = float(rotated_dims[1])
                actual_depth = float(rotated_dims[2])
            else:
                actual_width = float(item.width)
                actual_height = float(item.height)
                actual_depth = float(item.depth)

            self.packing_results['packed_items_details'].append({
                'item_name': original_name,
                'instance_name': item.name,
                'position_x': float(item.position[0]),
                'position_y': float(item.position[1]),
                'position_z': float(item.position[2]),
                'original_width_m': float(item.width),
                'original_height_m': float(item.height),
                'original_depth_m': float(item.depth),
                'actual_width_m': actual_width,
                'actual_height_m': actual_height,
                'actual_depth_m': actual_depth,
                'rotation_type': rotation_type,
                'rotation_description': rotation_description,
                'weight_kg': float(item.weight),
                'volume_m3': float(item.width * item.height * item.depth)
            })

        # Print summary
        print(f"\n=== OPTIMIZATION METRICS ===")
        print(f"Volume Utilization: {volume_utilization:.2f}%")
        print(f"Weight Utilization: {weight_utilization:.2f}%")
        print(f"Total Packed Volume: {total_packed_volume:.3f} m³")
        print(f"Total Packed Weight: {total_packed_weight:.2f} kg")
        print(f"Container Volume: {self.container_specs['volume']:.3f} m³")
        print(f"Container Max Weight: {self.container_specs['max_weight']} kg")

        print(f"\n=== ITEMS PACKED BY TYPE ===")
        for item_name, count in item_counts.items():
            print(f"{item_name}: {count} units")

    def save_results_to_excel(self):
        """Save the packing results back to the Excel file using xlwings."""
        try:
            # Use the existing workbook if available
            if hasattr(self, 'workbook') and self.workbook:
                wb = self.workbook
            else:
                # Create new app instance if needed
                app = xw.App(visible=False, add_book=False)
                wb = app.books.open(self.excel_file_path)
                self.excel_app = app

            # Create or get Packing_Results sheet
            if self.packing_results['packed_items_details']:
                try:
                    ws_packing = wb.sheets['Packing_Results']
                    ws_packing.clear()
                except:
                    ws_packing = wb.sheets.add('Packing_Results')

                # Write headers
                headers = list(self.packing_results['packed_items_details'][0].keys())
                ws_packing.range('A1').value = headers

                # Write data
                data_rows = []
                for item in self.packing_results['packed_items_details']:
                    row = [item[header] for header in headers]
                    data_rows.append(row)

                ws_packing.range('A2').value = data_rows

                # Format the sheet
                ws_packing.range('A1').expand('right').api.Font.Bold = True
                ws_packing.autofit()

            # Create or get Summary_Statistics sheet
            try:
                ws_summary = wb.sheets['Summary_Statistics']
                ws_summary.clear()
            except:
                ws_summary = wb.sheets.add('Summary_Statistics')

            # Write summary data
            summary_data = [
                ['Metric', 'Value'],
                ['Total Items Packed', self.packing_results['total_items_packed']],
                ['Total Items Unpacked', self.packing_results['total_items_unpacked']],
                ['Volume Utilization (%)', f"{self.packing_results['volume_utilization_percent']:.2f}"],
                ['Weight Utilization (%)', f"{self.packing_results['weight_utilization_percent']:.2f}"],
                ['Total Packed Volume (m³)', f"{self.packing_results['total_packed_volume_m3']:.3f}"],
                ['Total Packed Weight (kg)', f"{self.packing_results['total_packed_weight_kg']:.2f}"],
                ['Container Volume (m³)', f"{self.packing_results['container_volume_m3']:.3f}"],
                ['Container Max Weight (kg)', self.packing_results['container_max_weight_kg']],
                ['Optimization Timestamp', datetime.now().strftime("%Y-%m-%d %H:%M:%S")]
            ]

            ws_summary.range('A1').value = summary_data
            ws_summary.range('A1').expand('right').api.Font.Bold = True
            ws_summary.autofit()

            # Create or get Item_Counts sheet
            if self.packing_results['item_counts']:
                try:
                    ws_counts = wb.sheets['Item_Counts']
                    ws_counts.clear()
                except:
                    ws_counts = wb.sheets.add('Item_Counts')

                # Write item counts data
                counts_data = [['Item_Name', 'Quantity_Packed']]
                for item_name, quantity in self.packing_results['item_counts'].items():
                    counts_data.append([item_name, quantity])

                ws_counts.range('A1').value = counts_data
                ws_counts.range('A1').expand('right').api.Font.Bold = True
                ws_counts.autofit()

            # Save the workbook
            wb.save()
            print(f"\nResults saved to {self.excel_file_path}")
            return True

        except Exception as e:
            print(f"Error saving results to Excel: {e}")
            return False

    def save_batch_results_to_excel(self, batch_id):
        """Save the packing results for a specific batch to Excel."""
        try:
            # Use the existing workbook if available
            if hasattr(self, 'workbook') and self.workbook:
                wb = self.workbook
            else:
                # Create new app instance if needed
                app = xw.App(visible=False, add_book=False)
                wb = app.books.open(self.excel_file_path)
                self.excel_app = app

            # Create or get batch-specific sheets
            if self.packing_results['packed_items_details']:
                sheet_name = f'Packing_Results_{batch_id}'
                try:
                    ws_packing = wb.sheets[sheet_name]
                    ws_packing.clear()
                except:
                    ws_packing = wb.sheets.add(sheet_name)

                # Write headers
                headers = list(self.packing_results['packed_items_details'][0].keys())
                ws_packing.range('A1').value = headers

                # Write data
                data_rows = []
                for item in self.packing_results['packed_items_details']:
                    row = [item[header] for header in headers]
                    data_rows.append(row)

                ws_packing.range('A2').value = data_rows

                # Format the sheet
                ws_packing.range('A1').expand('right').api.Font.Bold = True
                ws_packing.autofit()

            # Create or get batch-specific summary sheet
            summary_sheet_name = f'Summary_{batch_id}'
            try:
                ws_summary = wb.sheets[summary_sheet_name]
                ws_summary.clear()
            except:
                ws_summary = wb.sheets.add(summary_sheet_name)

            # Write summary data
            summary_data = [
                ['Metric', 'Value'],
                ['Batch ID', batch_id],
                ['Total Items Packed', self.packing_results['total_items_packed']],
                ['Total Items Unpacked', self.packing_results['total_items_unpacked']],
                ['Volume Utilization (%)', f"{self.packing_results['volume_utilization_percent']:.2f}"],
                ['Weight Utilization (%)', f"{self.packing_results['weight_utilization_percent']:.2f}"],
                ['Total Packed Volume (m³)', f"{self.packing_results['total_packed_volume_m3']:.3f}"],
                ['Total Packed Weight (kg)', f"{self.packing_results['total_packed_weight_kg']:.2f}"],
                ['Container Volume (m³)', f"{self.packing_results['container_volume_m3']:.3f}"],
                ['Container Max Weight (kg)', self.packing_results['container_max_weight_kg']],
                ['Optimization Timestamp', datetime.now().strftime("%Y-%m-%d %H:%M:%S")]
            ]

            ws_summary.range('A1').value = summary_data
            ws_summary.range('A1').expand('right').api.Font.Bold = True
            ws_summary.autofit()

            # Create or get batch-specific item counts sheet
            if self.packing_results['item_counts']:
                counts_sheet_name = f'Item_Counts_{batch_id}'
                try:
                    ws_counts = wb.sheets[counts_sheet_name]
                    ws_counts.clear()
                except:
                    ws_counts = wb.sheets.add(counts_sheet_name)

                # Write item counts data
                counts_data = [['Item_Name', 'Quantity_Packed']]
                for item_name, quantity in self.packing_results['item_counts'].items():
                    counts_data.append([item_name, quantity])

                ws_counts.range('A1').value = counts_data
                ws_counts.range('A1').expand('right').api.Font.Bold = True
                ws_counts.autofit()

            # Save the workbook
            wb.save()
            print(f"\nBatch {batch_id} results saved to {self.excel_file_path}")
            return True

        except Exception as e:
            print(f"Error saving batch results to Excel: {e}")
            return False

    def save_item_results_to_excel(self, item_id):
        """Save the packing results for a specific item to Excel."""
        try:
            # Use the existing workbook if available
            if hasattr(self, 'workbook') and self.workbook:
                wb = self.workbook
            else:
                # Create new app instance if needed
                app = xw.App(visible=False, add_book=False)
                wb = app.books.open(self.excel_file_path)
                self.excel_app = app

            # Create or get item-specific sheets
            if self.packing_results['packed_items_details']:
                sheet_name = f'Packing_Results_{item_id}'
                try:
                    ws_packing = wb.sheets[sheet_name]
                    ws_packing.clear()
                except:
                    ws_packing = wb.sheets.add(sheet_name)

                # Write headers
                headers = list(self.packing_results['packed_items_details'][0].keys())
                ws_packing.range('A1').value = headers

                # Write data
                data_rows = []
                for item in self.packing_results['packed_items_details']:
                    row = [item[header] for header in headers]
                    data_rows.append(row)

                ws_packing.range('A2').value = data_rows

                # Format the sheet
                ws_packing.range('A1').expand('right').api.Font.Bold = True
                ws_packing.autofit()

            # Create or get item-specific summary sheet
            summary_sheet_name = f'Summary_{item_id}'
            try:
                ws_summary = wb.sheets[summary_sheet_name]
                ws_summary.clear()
            except:
                ws_summary = wb.sheets.add(summary_sheet_name)

            # Write summary data
            summary_data = [
                ['Metric', 'Value'],
                ['Item ID', item_id],
                ['Item Description', getattr(self, 'current_item', {}).get('item_description', 'N/A')],
                ['Total Boxes Packed', self.packing_results['total_items_packed']],
                ['Total Boxes Unpacked', self.packing_results['total_items_unpacked']],
                ['Volume Utilization (%)', f"{self.packing_results['volume_utilization_percent']:.2f}"],
                ['Weight Utilization (%)', f"{self.packing_results['weight_utilization_percent']:.2f}"],
                ['Total Packed Volume (m³)', f"{self.packing_results['total_packed_volume_m3']:.3f}"],
                ['Total Packed Weight (kg)', f"{self.packing_results['total_packed_weight_kg']:.2f}"],
                ['Container Volume (m³)', f"{self.packing_results['container_volume_m3']:.3f}"],
                ['Container Max Weight (kg)', self.packing_results['container_max_weight_kg']],
                ['Equal Box Counts', getattr(self, 'current_item', {}).get('equal_box_counts', 'N/A')],
                ['Optimization Timestamp', datetime.now().strftime("%Y-%m-%d %H:%M:%S")]
            ]

            ws_summary.range('A1').value = summary_data
            ws_summary.range('A1').expand('right').api.Font.Bold = True
            ws_summary.autofit()

            # Create or get item-specific box counts sheet
            if self.packing_results['item_counts']:
                counts_sheet_name = f'Box_Counts_{item_id}'
                try:
                    ws_counts = wb.sheets[counts_sheet_name]
                    ws_counts.clear()
                except:
                    ws_counts = wb.sheets.add(counts_sheet_name)

                # Write box counts data
                counts_data = [['Box_Type', 'Quantity_Packed']]
                for box_name, quantity in self.packing_results['item_counts'].items():
                    counts_data.append([box_name, quantity])

                ws_counts.range('A1').value = counts_data
                ws_counts.range('A1').expand('right').api.Font.Bold = True
                ws_counts.autofit()

            # Save the workbook
            wb.save()
            print(f"\nItem {item_id} results saved to {self.excel_file_path}")
            return True

        except Exception as e:
            print(f"Error saving item results to Excel: {e}")
            return False

    def print_detailed_results(self):
        """Print comprehensive results to console."""
        if not self.packing_results:
            print("No packing results available")
            return

        print("\n" + "="*80)
        print("CONTAINER PACKING OPTIMIZATION RESULTS")
        print("="*80)

        print(f"\nCONTAINER SPECIFICATIONS (40HQ):")
        print(f"  Dimensions: {self.container_specs['length']:.3f}m x {self.container_specs['width']:.3f}m x {self.container_specs['height']:.3f}m")
        print(f"  Volume: {self.container_specs['volume']:.3f} m³")
        print(f"  Max Weight: {self.container_specs['max_weight']:,} kg")

        print(f"\nPACKING SUMMARY:")
        print(f"  Items Successfully Packed: {self.packing_results['total_items_packed']}")
        print(f"  Items Not Packed: {self.packing_results['total_items_unpacked']}")
        print(f"  Volume Utilization: {self.packing_results['volume_utilization_percent']:.2f}%")
        print(f"  Weight Utilization: {self.packing_results['weight_utilization_percent']:.2f}%")
        print(f"  Total Packed Volume: {self.packing_results['total_packed_volume_m3']:.3f} m³")
        print(f"  Total Packed Weight: {self.packing_results['total_packed_weight_kg']:.2f} kg")

        print(f"\nITEMS PACKED BY TYPE:")
        for item_name, count in self.packing_results['item_counts'].items():
            print(f"  {item_name}: {count} units")

        print("\n" + "="*80)

    def close_workbook(self):
        """Keep the Excel workbook open - no longer closes automatically."""
        try:
            if hasattr(self, 'workbook') and self.workbook:
                print("Excel workbook remains open for user access")
            if hasattr(self, 'excel_app') and self.excel_app:
                # Make Excel visible so user can see it
                self.excel_app.visible = True
                print("Excel application is now visible and remains open")
        except Exception as e:
            print(f"Error making Excel visible: {e}")

    def _get_rotation_description(self, rotation_type):
        """
        Get human-readable description of rotation type.

        Args:
            rotation_type (int): The rotation type from py3dbp

        Returns:
            str: Description of the rotation
        """
        rotation_descriptions = {
            0: "No rotation (W×H×D)",
            1: "90° Z-axis rotation (H×W×D)",
            2: "90° Y-axis rotation (D×H×W)",
            3: "90° X-axis rotation (W×D×H)",
            4: "Z+Y rotation (H×D×W)",
            5: "Z+X rotation (D×W×H)"
        }
        return rotation_descriptions.get(rotation_type, f"Unknown rotation type {rotation_type}")

    def create_3d_visualization(self, save_html=True, show_plot=True, max_items_to_show=None, html_filename=None):
        """
        Create a 3D visualization of the packed items using Plotly.

        Args:
            save_html (bool): Whether to save the plot as HTML file
            show_plot (bool): Whether to display the plot
            max_items_to_show (int): Maximum number of items to show, None for all items
        """
        if not self.packing_results or not self.packing_results['packed_items_details']:
            print("No packing results available for visualization")
            return None

        # Create figure with subplots for different views
        from plotly.subplots import make_subplots

        # Create main 3D plot
        fig = go.Figure()

        # Add container outline - correct coordinate mapping
        # Based on py3dbp: X=width, Y=height, Z=depth
        container_x = self.container_specs['width']   # 2.352m
        container_y = self.container_specs['height']  # 2.698m
        container_z = self.container_specs['length']  # 12.032m

        # Container wireframe - make it more prominent
        container_vertices = [
            [0, 0, 0], [container_x, 0, 0], [container_x, container_y, 0], [0, container_y, 0],  # bottom
            [0, 0, container_z], [container_x, 0, container_z], [container_x, container_y, container_z], [0, container_y, container_z]  # top
        ]

        # Container edges
        container_edges = [
            [0, 1], [1, 2], [2, 3], [3, 0],  # bottom edges
            [4, 5], [5, 6], [6, 7], [7, 4],  # top edges
            [0, 4], [1, 5], [2, 6], [3, 7]   # vertical edges
        ]

        # Add container wireframe with thicker lines
        for i, edge in enumerate(container_edges):
            start, end = edge
            fig.add_trace(go.Scatter3d(
                x=[container_vertices[start][0], container_vertices[end][0]],
                y=[container_vertices[start][1], container_vertices[end][1]],
                z=[container_vertices[start][2], container_vertices[end][2]],
                mode='lines',
                line=dict(color='red', width=6),
                name='Container Outline' if i == 0 else '',
                showlegend=True if i == 0 else False,
                hoverinfo='skip'
            ))

        # Show all items or limit based on parameter
        total_items = len(self.packing_results['packed_items_details'])
        if max_items_to_show is None:
            items_to_show = self.packing_results['packed_items_details']
            print(f"Showing all {total_items} items with wireframes")
        else:
            items_to_show = self.packing_results['packed_items_details'][:max_items_to_show]
            print(f"Showing {len(items_to_show)} out of {total_items} items")

        # Create a more efficient visualization using scatter3d with custom markers
        x_positions = []
        y_positions = []
        z_positions = []
        hover_texts = []
        colors = []
        sizes = []

        # Use a color scale for better distinction
        import random
        random.seed(42)  # For consistent colors

        for i, item in enumerate(items_to_show):
            x_pos = item['position_x']
            y_pos = item['position_y']
            z_pos = item['position_z']
            width = item['actual_width_m']
            height = item['actual_height_m']
            depth = item['actual_depth_m']

            # Add center point of each box
            x_positions.append(x_pos + width/2)
            y_positions.append(y_pos + height/2)
            z_positions.append(z_pos + depth/2)

            # Create hover text
            hover_text = (f"<b>Item #{i+1}</b><br>" +
                         f"Position: ({x_pos:.2f}, {y_pos:.2f}, {z_pos:.2f})<br>" +
                         f"Dimensions: {width:.2f} × {height:.2f} × {depth:.2f} m<br>" +
                         f"Rotation: {item['rotation_description']}<br>" +
                         f"Weight: {item['weight_kg']:.2f} kg<br>" +
                         f"Volume: {item['volume_m3']:.3f} m³")
            hover_texts.append(hover_text)

            # Color based on position for better distinction
            colors.append(i)

            # Size based on volume
            volume = width * height * depth
            sizes.append(max(5, min(20, volume * 50)))  # Scale size appropriately

        # Add scatter plot for item centers
        fig.add_trace(go.Scatter3d(
            x=x_positions,
            y=y_positions,
            z=z_positions,
            mode='markers',
            marker=dict(
                size=sizes,
                color=colors,
                colorscale='Viridis',
                opacity=0.8,
                line=dict(width=2, color='black')
            ),
            text=hover_texts,
            hovertemplate='%{text}<extra></extra>',
            name=f'Packed Items (showing {len(items_to_show)})'
        ))

        # Add wireframe boxes for all items to show actual placement
        print(f"Creating wireframes for all {len(items_to_show)} items...")
        for i, item in enumerate(items_to_show):
            x_pos = item['position_x']
            y_pos = item['position_y']
            z_pos = item['position_z']
            width = item['actual_width_m']
            height = item['actual_height_m']
            depth = item['actual_depth_m']

            # Create wireframe box with all 12 edges
            # Bottom face edges
            bottom_edges = [
                ([x_pos, x_pos+width], [y_pos, y_pos], [z_pos, z_pos]),  # front bottom
                ([x_pos+width, x_pos+width], [y_pos, y_pos+height], [z_pos, z_pos]),  # right bottom
                ([x_pos+width, x_pos], [y_pos+height, y_pos+height], [z_pos, z_pos]),  # back bottom
                ([x_pos, x_pos], [y_pos+height, y_pos], [z_pos, z_pos])  # left bottom
            ]

            # Top face edges
            top_edges = [
                ([x_pos, x_pos+width], [y_pos, y_pos], [z_pos+depth, z_pos+depth]),  # front top
                ([x_pos+width, x_pos+width], [y_pos, y_pos+height], [z_pos+depth, z_pos+depth]),  # right top
                ([x_pos+width, x_pos], [y_pos+height, y_pos+height], [z_pos+depth, z_pos+depth]),  # back top
                ([x_pos, x_pos], [y_pos+height, y_pos], [z_pos+depth, z_pos+depth])  # left top
            ]

            # Vertical edges
            vertical_edges = [
                ([x_pos, x_pos], [y_pos, y_pos], [z_pos, z_pos+depth]),  # front left
                ([x_pos+width, x_pos+width], [y_pos, y_pos], [z_pos, z_pos+depth]),  # front right
                ([x_pos+width, x_pos+width], [y_pos+height, y_pos+height], [z_pos, z_pos+depth]),  # back right
                ([x_pos, x_pos], [y_pos+height, y_pos+height], [z_pos, z_pos+depth])  # back left
            ]

            all_edges = bottom_edges + top_edges + vertical_edges

            # Use different colors for better distinction
            color_cycle = [
                'rgba(255, 0, 0, 0.8)',    # Red
                'rgba(0, 255, 0, 0.8)',    # Green
                'rgba(0, 0, 255, 0.8)',    # Blue
                'rgba(255, 255, 0, 0.8)',  # Yellow
                'rgba(255, 0, 255, 0.8)',  # Magenta
                'rgba(0, 255, 255, 0.8)',  # Cyan
                'rgba(255, 128, 0, 0.8)',  # Orange
                'rgba(128, 0, 255, 0.8)',  # Purple
                'rgba(255, 192, 203, 0.8)', # Pink
                'rgba(0, 128, 0, 0.8)'     # Dark Green
            ]

            item_color = color_cycle[i % len(color_cycle)]

            # Combine all edges into single trace per item for better performance
            all_x = []
            all_y = []
            all_z = []

            for edge_x, edge_y, edge_z in all_edges:
                all_x.extend(edge_x + [None])  # None creates line breaks
                all_y.extend(edge_y + [None])
                all_z.extend(edge_z + [None])

            fig.add_trace(go.Scatter3d(
                x=all_x,
                y=all_y,
                z=all_z,
                mode='lines',
                line=dict(color=item_color, width=2),
                name=f'Item {i+1}' if i < 10 else '',
                showlegend=True if i < 10 else False,
                hovertemplate=f"<b>Item #{i+1}</b><br>" +
                            f"Position: ({x_pos:.2f}, {y_pos:.2f}, {z_pos:.2f})<br>" +
                            f"Dimensions: {width:.2f} × {height:.2f} × {depth:.2f} m<br>" +
                            f"Rotation: {item['rotation_description']}<extra></extra>"
            ))

            # Progress indicator for large numbers of items
            if (i + 1) % 50 == 0:
                print(f"  Created wireframes for {i + 1} items...")

        # Calculate actual space utilization for better visualization
        if items_to_show:
            max_x_used = max(item['position_x'] + item['actual_width_m'] for item in items_to_show)
            max_y_used = max(item['position_y'] + item['actual_height_m'] for item in items_to_show)
            max_z_used = max(item['position_z'] + item['actual_depth_m'] for item in items_to_show)

            # Add small margin for better visualization
            margin = 0.1
            x_range = [0, max(max_x_used + margin, container_x)]
            y_range = [0, max(max_y_used + margin, container_y)]
            z_range = [0, max(max_z_used + margin, container_z)]
        else:
            x_range = [0, container_x]
            y_range = [0, container_y]
            z_range = [0, container_z]

        # Update layout with better styling and correct proportions
        fig.update_layout(
            title=dict(
                text=f"3D Container Packing Visualization - 40HQ Container<br>" +
                     f"<sub>Showing {len(items_to_show)} of {total_items} items | " +
                     f"Volume Utilization: {self.packing_results['volume_utilization_percent']:.1f}% | " +
                     f"Space Used: X={max_x_used:.1f}m/{container_x:.1f}m, Y={max_y_used:.1f}m/{container_y:.1f}m, Z={max_z_used:.1f}m/{container_z:.1f}m</sub>",
                x=0.5,
                font=dict(size=16)
            ),
            scene=dict(
                xaxis_title="Width (m) - Container Dimension: 2.352m",
                yaxis_title="Height (m) - Container Dimension: 2.698m",
                zaxis_title="Depth/Length (m) - Container Dimension: 12.032m",
                xaxis=dict(
                    range=x_range,
                    showgrid=True,
                    gridcolor='lightgray',
                    gridwidth=1,
                    dtick=0.5
                ),
                yaxis=dict(
                    range=y_range,
                    showgrid=True,
                    gridcolor='lightgray',
                    gridwidth=1,
                    dtick=0.5
                ),
                zaxis=dict(
                    range=z_range,
                    showgrid=True,
                    gridcolor='lightgray',
                    gridwidth=1,
                    dtick=1.0
                ),
                aspectmode='data',
                bgcolor='white',
                camera=dict(
                    eye=dict(x=1.2, y=1.2, z=0.8)
                )
            ),
            width=1400,
            height=1000,
            margin=dict(l=0, r=0, t=100, b=0),
            showlegend=True,
            legend=dict(
                x=0.02,
                y=0.98,
                bgcolor='rgba(255,255,255,0.9)',
                bordercolor='black',
                borderwidth=1
            )
        )

        # Add annotations with key statistics
        if items_to_show:
            x_util = (max_x_used / container_x) * 100
            y_util = (max_y_used / container_y) * 100
            z_util = (max_z_used / container_z) * 100

            fig.add_annotation(
                x=0.02, y=0.02,
                xref='paper', yref='paper',
                text=f"Container: {container_x:.1f}m × {container_y:.1f}m × {container_z:.1f}m<br>" +
                     f"Space Used: {max_x_used:.1f}m × {max_y_used:.1f}m × {max_z_used:.1f}m<br>" +
                     f"Utilization: X={x_util:.1f}%, Y={y_util:.1f}%, Z={z_util:.1f}%<br>" +
                     f"Volume: {self.packing_results['total_packed_volume_m3']:.1f}/{self.container_specs['volume']:.1f} m³ ({self.packing_results['volume_utilization_percent']:.1f}%)<br>" +
                     f"Items Packed: {total_items}",
                showarrow=False,
                bgcolor='rgba(255,255,255,0.9)',
                bordercolor='black',
                borderwidth=1,
                font=dict(size=11)
            )

        # Save as HTML if requested
        if save_html:
            if html_filename is None:
                html_filename = "container_packing_3d_visualization.html"

            # Ensure file is saved in the same directory as the script
            import os
            script_dir = os.path.dirname(os.path.abspath(__file__))
            full_path = os.path.join(script_dir, html_filename)

            fig.write_html(full_path)
            print(f"3D visualization saved as: {full_path}")

        # Show plot if requested
        if show_plot:
            fig.show()

        return fig

    def create_2d_cross_sections(self, save_html=True, show_plot=True, html_filename=None):
        """Create 2D cross-section views of the packing."""
        from plotly.subplots import make_subplots

        # Create subplots for different views
        from plotly.subplots import make_subplots
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('Top View (Width×Height)', 'Front View (Width×Depth)', 'Side View (Height×Depth)', 'Packing Statistics'),
            specs=[[{'type': 'scatter'}, {'type': 'scatter'}],
                   [{'type': 'scatter'}, {'type': 'table'}]]
        )

        items = self.packing_results['packed_items_details']

        # Top view (X-Y plane) - show all items
        for item in items:
            x_pos = item['position_x']
            y_pos = item['position_y']
            width = item['actual_width_m']
            height = item['actual_height_m']

            # Draw rectangle with better visibility for overlapping items
            fig.add_shape(
                type="rect",
                x0=x_pos, y0=y_pos,
                x1=x_pos + width, y1=y_pos + height,
                line=dict(color="blue", width=1),
                fillcolor="lightblue",
                opacity=0.3,  # Lower opacity to see overlapping items
                row=1, col=1
            )

        # Front view (X-Z plane) - show all items
        for item in items:
            x_pos = item['position_x']
            z_pos = item['position_z']
            width = item['actual_width_m']
            depth = item['actual_depth_m']

            fig.add_shape(
                type="rect",
                x0=x_pos, y0=z_pos,
                x1=x_pos + width, y1=z_pos + depth,
                line=dict(color="green", width=1),
                fillcolor="lightgreen",
                opacity=0.3,  # Lower opacity to see overlapping items
                row=1, col=2
            )

        # Side view (Y-Z plane) - show all items
        for item in items:
            y_pos = item['position_y']
            z_pos = item['position_z']
            height = item['actual_height_m']
            depth = item['actual_depth_m']

            fig.add_shape(
                type="rect",
                x0=y_pos, y0=z_pos,
                x1=y_pos + height, y1=z_pos + depth,
                line=dict(color="red", width=1),
                fillcolor="lightcoral",
                opacity=0.3,  # Lower opacity to see overlapping items
                row=2, col=1
            )

        # Add container outlines - correct coordinate mapping
        # X=width, Y=height, Z=depth
        container_x = self.container_specs['width']   # 2.352m
        container_y = self.container_specs['height']  # 2.698m
        container_z = self.container_specs['length']  # 12.032m

        # Container outline for each view
        # Top view (X-Y): width x height
        # Front view (X-Z): width x depth
        # Side view (Y-Z): height x depth
        for row, col, x_max, y_max in [(1, 1, container_x, container_y),
                                       (1, 2, container_x, container_z),
                                       (2, 1, container_y, container_z)]:
            fig.add_shape(
                type="rect",
                x0=0, y0=0, x1=x_max, y1=y_max,
                line=dict(color="black", width=3),
                fillcolor="rgba(0,0,0,0)",
                row=row, col=col
            )

        # Add statistics table
        stats_data = [
            ['Total Items Packed', self.packing_results['total_items_packed']],
            ['Volume Utilization', f"{self.packing_results['volume_utilization_percent']:.1f}%"],
            ['Weight Utilization', f"{self.packing_results['weight_utilization_percent']:.1f}%"],
            ['Container Volume', f"{self.container_specs['volume']:.1f} m³"],
            ['Used Volume', f"{self.packing_results['total_packed_volume_m3']:.1f} m³"],
            ['Total Weight', f"{self.packing_results['total_packed_weight_kg']:.1f} kg"]
        ]

        fig.add_trace(go.Table(
            header=dict(values=['Metric', 'Value'],
                       fill_color='lightblue',
                       align='left'),
            cells=dict(values=[[row[0] for row in stats_data],
                              [row[1] for row in stats_data]],
                      fill_color='white',
                      align='left')
        ), row=2, col=2)

        # Update layout
        fig.update_layout(
            title_text="Container Packing - 2D Cross-Section Views",
            showlegend=False,
            height=800,
            width=1200
        )

        # Update axes with correct labels and consistent scaling
        # Set consistent ranges for all views
        width_range = [0, container_x + 0.2]    # 0 to 2.552m
        height_range = [0, container_y + 0.2]   # 0 to 2.898m
        depth_range = [0, container_z + 0.5]    # 0 to 12.532m

        # Top view (Width × Height)
        fig.update_xaxes(
            title_text="Width (m)",
            range=width_range,
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            dtick=0.5,
            row=1, col=1
        )
        fig.update_yaxes(
            title_text="Height (m)",
            range=height_range,
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            dtick=0.5,
            row=1, col=1
        )

        # Front view (Width × Depth)
        fig.update_xaxes(
            title_text="Width (m)",
            range=width_range,
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            dtick=0.5,
            row=1, col=2
        )
        fig.update_yaxes(
            title_text="Depth (m)",
            range=depth_range,
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            dtick=1.0,
            row=1, col=2
        )

        # Side view (Height × Depth)
        fig.update_xaxes(
            title_text="Height (m)",
            range=height_range,
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            dtick=0.5,
            row=2, col=1
        )
        fig.update_yaxes(
            title_text="Depth (m)",
            range=depth_range,
            showgrid=True,
            gridwidth=1,
            gridcolor='lightgray',
            dtick=1.0,
            row=2, col=1
        )

        if save_html:
            if html_filename is None:
                html_filename = "container_packing_2d_views.html"

            # Ensure file is saved in the same directory as the script
            import os
            script_dir = os.path.dirname(os.path.abspath(__file__))
            full_path = os.path.join(script_dir, html_filename)

            fig.write_html(full_path)
            print(f"2D cross-section views saved as: {full_path}")

        # Show plot if requested
        if show_plot:
            fig.show()

        return fig

def create_item_summary_report(excel_file, results):
    """Create a summary report for all processed items."""
    try:
        wb = xw.Book(excel_file)

        # Create or get Item_Summary sheet
        try:
            ws_summary = wb.sheets['Item_Summary']
            ws_summary.clear()
        except:
            ws_summary = wb.sheets.add('Item_Summary')

        # Create summary data
        summary_data = [
            ['Item_ID', 'Item_Description', 'Boxes_Packed', 'Volume_Utilization_%', 'Weight_Utilization_%', 'Total_Volume_m3', 'Total_Weight_kg', 'Equal_Box_Counts', 'Timestamp']
        ]

        for item_id, result in results.items():
            item_info = result['item_info']
            packing_results = result['results']

            summary_data.append([
                item_id,
                item_info['item_description'],
                packing_results['total_items_packed'],
                f"{packing_results['volume_utilization_percent']:.2f}",
                f"{packing_results['weight_utilization_percent']:.2f}",
                f"{packing_results['total_packed_volume_m3']:.3f}",
                f"{packing_results['total_packed_weight_kg']:.2f}",
                'YES' if item_info['equal_box_counts'] else 'NO',
                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            ])

        # Write summary data
        ws_summary.range('A1').value = summary_data
        ws_summary.range('A1').expand('right').api.Font.Bold = True
        ws_summary.autofit()

        # Add comparison chart data
        chart_data = [
            ['Item_ID', 'Volume_Utilization_%', 'Weight_Utilization_%']
        ]

        for item_id, result in results.items():
            packing_results = result['results']
            chart_data.append([
                item_id,
                packing_results['volume_utilization_percent'],
                packing_results['weight_utilization_percent']
            ])

        # Write chart data starting from column K
        ws_summary.range('K1').value = chart_data
        ws_summary.range('K1').expand('right').api.Font.Bold = True

        wb.save()
        print(f"Item summary report created in 'Item_Summary' sheet")

    except Exception as e:
        print(f"Error creating item summary report: {e}")

def process_single_item(excel_file, item_id=None):
    """Process a single item optimization."""
    print(f"\n{'='*80}")
    if item_id:
        print(f"PROCESSING ITEM: {item_id}")
    else:
        print("PROCESSING TRADITIONAL SINGLE BATCH")
    print(f"{'='*80}")

    # Initialize optimizer
    optimizer = ContainerPackingOptimizer(excel_file)

    # Setup item structure if needed
    if item_id:
        optimizer.setup_batch_structure()
        optimizer.cleanup_obsolete_sheets()

    # Read items from Excel
    if not optimizer.read_items_from_excel(item_id):
        print(f"Failed to read boxes for item {item_id}")
        return None

    # Determine optimization settings
    equal_item_counts = True
    if hasattr(optimizer, 'current_item'):
        equal_item_counts = optimizer.current_item['equal_box_counts']
        print(f"Item {item_id}: Equal box counts = {equal_item_counts}")

    # Run optimization with high limit to maximize utilization
    # The algorithm will find the actual maximum based on container constraints
    if not optimizer.optimize_packing(max_instances_per_item=10000, equal_item_counts=equal_item_counts):
        print("Optimization failed")
        return None

    # Print detailed results
    optimizer.print_detailed_results()

    # Save results to Excel with item suffix
    if item_id:
        optimizer.save_item_results_to_excel(item_id)
    else:
        optimizer.save_results_to_excel()

    # Create visualizations only if enabled
    if optimizer.enable_3d_wireframe:
        print("\nCreating complete 3D visualization with all boxes...")
        html_file = f"container_packing_3d_{item_id}.html" if item_id else "container_packing_3d_visualization.html"
        optimizer.create_3d_visualization(save_html=True, show_plot=False, max_items_to_show=None, html_filename=html_file)
    else:
        print("\n3D wireframe visualization disabled for faster processing")

    if optimizer.enable_2d_plots:
        print("Creating 2D cross-section views...")
        html_file = f"container_packing_2d_{item_id}.html" if item_id else "container_packing_2d_views.html"
        optimizer.create_2d_cross_sections(save_html=True, show_plot=False, html_filename=html_file)
    else:
        print("2D cross-section plots disabled for faster processing")

    print(f"\nItem optimization completed successfully!")
    print(f"Maximum boxes that can be packed: {optimizer.packing_results['total_items_packed']}")
    print(f"Volume utilization: {optimizer.packing_results['volume_utilization_percent']:.2f}%")
    print(f"Weight utilization: {optimizer.packing_results['weight_utilization_percent']:.2f}%")

    return optimizer

def process_multiple_items(excel_file):
    """Process multiple items from Excel configuration."""
    print("="*80)
    print("CONTAINER PACKING OPTIMIZATION - MULTIPLE ITEMS")
    print("="*80)

    # Initialize optimizer to read item configuration
    temp_optimizer = ContainerPackingOptimizer(excel_file)
    temp_optimizer.setup_batch_structure()
    temp_optimizer.cleanup_obsolete_sheets()

    try:
        wb = xw.Book(excel_file)

        # Read item configuration
        item_batches_ws = wb.sheets['Item_Batches']
        config_data = item_batches_ws.used_range.value

        active_items = []
        for row in config_data[1:]:  # Skip header
            if row and row[2] == 'YES':  # Active column
                active_items.append({
                    'item_id': row[0],
                    'item_description': row[1],
                    'equal_box_counts': row[3] == 'YES'
                })

        if not active_items:
            print("No active items found. Please set at least one item to 'YES' in the Active column.")
            return

        print(f"Found {len(active_items)} active items:")
        for item in active_items:
            print(f"  - {item['item_id']}: {item['item_description']}")

        # Process each active item
        results = {}
        for item in active_items:
            item_id = item['item_id']
            optimizer = process_single_item(excel_file, item_id)
            if optimizer:
                results[item_id] = {
                    'optimizer': optimizer,
                    'item_info': item,
                    'results': optimizer.packing_results
                }

        # Create summary report
        create_item_summary_report(excel_file, results)

        # Make Excel visible and keep it open
        if results:
            first_optimizer = list(results.values())[0]['optimizer']
            first_optimizer.close_workbook()

        print(f"\n{'='*80}")
        print("ALL ITEMS COMPLETED SUCCESSFULLY!")
        print(f"{'='*80}")

        # Print summary
        for item_id, result in results.items():
            item_info = result['item_info']
            packing_results = result['results']
            print(f"\n{item_id} ({item_info['item_description']}):")
            print(f"  Boxes packed: {packing_results['total_items_packed']}")
            print(f"  Volume utilization: {packing_results['volume_utilization_percent']:.2f}%")
            print(f"  Weight utilization: {packing_results['weight_utilization_percent']:.2f}%")

    except Exception as e:
        print(f"Error processing multiple items: {e}")

def main():
    """Main function to run the container packing optimization."""
    excel_file = "items_to_pack.xlsm"

    # Check if we should process multiple items or single item
    try:
        wb = xw.Book(excel_file)

        # Check if Item_Batches sheet exists
        try:
            item_batches_ws = wb.sheets['Item_Batches']
            config_data = item_batches_ws.used_range.value

            # Count active items
            active_count = 0
            for row in config_data[1:]:  # Skip header
                if row and row[2] == 'YES':  # Active column (index 2 for Item_Batches)
                    active_count += 1

            if active_count > 1:
                # Multiple active items - process all
                process_multiple_items(excel_file)
            elif active_count == 1:
                # Single active item - process that item
                for row in config_data[1:]:
                    if row and row[2] == 'YES':
                        item_id = row[0]
                        optimizer = process_single_item(excel_file, item_id)
                        if optimizer:
                            optimizer.close_workbook()
                        break
            else:
                # No active items - use traditional method
                print("No active items found. Using traditional single batch processing.")
                optimizer = process_single_item(excel_file)
                if optimizer:
                    optimizer.close_workbook()

        except:
            # No Item_Batches sheet - use traditional method
            print("No item configuration found. Using traditional single batch processing.")
            optimizer = process_single_item(excel_file)
            if optimizer:
                optimizer.close_workbook()

    except Exception as e:
        print(f"Error in main: {e}")
        # Fallback to traditional method
        optimizer = process_single_item(excel_file)
        if optimizer:
            optimizer.close_workbook()

if __name__ == "__main__":
    main()