{"version": 1, "disable_existing_loggers": false, "formatters": {"simple": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "datefmt": "%Y-%m-%d %I:%M:%S %p %Z"}, "complex": {"format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s - %(pathname)s - %(lineno)d", "datefmt": "%Y-%m-%d %I:%M:%S %p %Z"}}, "handlers": {"console": {"class": "logging.StreamHandler", "level": "INFO", "formatter": "simple", "stream": "ext://sys.stdout"}, "file": {"class": "logging.handlers.RotatingFileHandler", "level": "INFO", "formatter": "simple", "filename": "./logs/optimization.log", "maxBytes": 1048576, "backupCount": 3, "mode": "w"}}, "root": {"level": "INFO", "handlers": ["console", "file"]}}