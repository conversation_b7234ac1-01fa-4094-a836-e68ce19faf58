2025-07-01 12:19:35 PM India Standard Time - model.data_connectors - INFO - Query executed: with latest_manual_forecast_data as (
select *, ROW_NUMBER() OVER( partition by itemID order by Week ) WeekOrder from NaomiHome.dbo.nh_forecastdata nf 
where nf.forecastdate = ( select max(forecastdate) from NaomiHome.dbo.nh_forecastdata where type = 'Manual' )
)

select * from latest_manual_forecast_data 
where WeekOrder <= 51
order by ItemID, Week

2025-07-01 12:19:38 PM India Standard Time - model.data_connectors - INFO - Connection closed
2025-07-01 12:19:41 PM India Standard Time - model.data_connectors - INFO - Query executed: with latest_manual_forecast_data as (
select *, ROW_NUMBER() OVER( partition by itemID order by Week ) WeekOrder from NaomiHome.dbo.nh_forecastdata nf 
where nf.forecastdate = ( select max(forecastdate) from NaomiHome.dbo.nh_forecastdata where type = 'Manual' )
)

select distinct Week from latest_manual_forecast_data 
where WeekOrder <= 51
order by Week

2025-07-01 12:19:42 PM India Standard Time - model.data_connectors - INFO - Connection closed
2025-07-01 12:19:44 PM India Standard Time - model.data_connectors - INFO - Query executed: with inv as ( 
select itemid, sum(qty) curr_inventory, max(updated) LastUpdated from product_details_warehouse_inventory pdwi  
join product_details pd on pd.id = pdwi.itemid
join product p on pd.pid = p.id 
join manufacturer m on m.id = p.manufacturer_id
where m.name = 'Naomi Home' and pd.availability <= 2 
group by itemid
),

gm as (
select pd.id itemid, pd.standard_cost, pd.calculated_shipping_cost, pd.price, ( pd.price  - ( pd.standard_cost + pd.calculated_shipping_cost ) ) gross_margin
from product_details pd 
join product p on pd.pid = p.id 
join manufacturer m on m.id = p.manufacturer_id
where m.name = 'Naomi Home' and pd.availability <= 2 
)

select gm.*, inv.curr_inventory, inv.LastUpdated last_updated from gm 
left join inv on gm.itemid = inv.itemid 
order by curr_inventory desc

2025-07-01 12:19:46 PM India Standard Time - model.data_connectors - INFO - Connection closed
2025-07-01 12:19:49 PM India Standard Time - model.data_connectors - INFO - Query executed: select * from tbl_nh_items_po_planning where not 
( sku is null or factory is null or planner is null or [length]  is null or width is null or height is null or cuft is null)
and itemid in ( select distinct itemID from NaomiHome.dbo.nh_forecastdata where forecastdate = ( select max(forecastdate) from NaomiHome.dbo.nh_forecastdata where type = 'Manual' ) )
order by [unique identifier - for container product mix]

2025-07-01 12:19:49 PM India Standard Time - model.data_connectors - INFO - Connection closed
2025-07-01 12:19:51 PM India Standard Time - model.data_connectors - INFO - Query executed: with item_arrivals_by_warehouse as ( 

    SELECT 
        w.name AS warehouse,
        poi.itemid,
        po.ref_number,
        CAST(po.po_expected_date AS DATE) AS expectedDate,
        poi.qty AS PO_qty,
        CONCAT( DATEPART(yyyy,po.po_expected_date) ,'-' , DATEPART(ww,po.po_expected_date) ) WeekYr
    FROM purchase_order (NOLOCK) po
    JOIN purchase_order_items (NOLOCK) poi ON po.id = poi.poid
    LEFT JOIN purchase_order_items_receiving (NOLOCK) poir ON poir.poiid = poi.id
    JOIN product_details (NOLOCK) pd ON pd.id = poi.itemid
    JOIN product (NOLOCK) p ON p.id = pd.pid
    JOIN warehouse (NOLOCK) w ON w.id = po.po_warehouse_id
    WHERE p.manufacturer_id = 3567 
        AND po.po_type = 'WAREHOUSE'
        AND poi.qty IS NOT NULL
        AND po.po_expected_date IS NOT NULL
        AND (po.status IS NULL OR po.status NOT IN ('CLOSED', 'CANCELLED', 'HOLD'))
        AND (po.ltl_waybill_ref IS NOT NULL AND po.ltl_waybill_ref <> '')
        AND w.name IN ('ARMS Logistics','FCI')
    

    UNION 
    
  
    SELECT 
    w.name  AS warehouse,
    poi.itemid,
    po.ref_number,
    CAST(po.po_expected_date AS DATE) AS expectedDate,
    poi.qty AS PO_qty,
    CONCAT( DATEPART(yyyy,po.po_expected_date) ,'-' , DATEPART(ww,po.po_expected_date) ) WeekYr
    FROM purchase_order (NOLOCK) po
    JOIN purchase_order_items (NOLOCK) poi ON po.id = poi.poid
    LEFT JOIN purchase_order_items_receiving (NOLOCK) poir ON poir.poiid = poi.id
    JOIN product_details (NOLOCK) pd ON pd.id = poi.itemid
    JOIN product (NOLOCK) p ON p.id = pd.pid
    JOIN warehouse (NOLOCK) w ON w.id = po.po_warehouse_id
    WHERE p.manufacturer_id = 3567 
        AND po.po_type = 'WAREHOUSE'
        AND poi.qty IS NOT NULL
        AND po.po_expected_date IS NOT NULL
        AND (po.status IS NULL OR po.status NOT IN ('CLOSED', 'CANCELLED', 'HOLD'))
        AND (po.ltl_waybill_ref IS NULL OR po.ltl_waybill_ref = '')
        AND w.name  IN ('ARMS Logistics','FCI')

    )


select itemid, WeekYr, sum(PO_qty) total_arrival_qty from item_arrivals_by_warehouse
group by itemid, WeekYr
order by 2

2025-07-01 12:19:52 PM India Standard Time - model.data_connectors - INFO - Connection closed
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Selected Items:
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Forecast Items
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - ---------------
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - [2404655, 2404656, 2326058, 2326059, 2326060]
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Forecast Data for the selected items for optimization
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - -----------------------------------------------------
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Forecast Periods
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - ['2025-19', '2025-20', '2025-21', '2025-22', '2025-23', '2025-24', '2025-25', '2025-26', '2025-27', '2025-28', '2025-29', '2025-30', '2025-31', '2025-32', '2025-33', '2025-34', '2025-35', '2025-36', '2025-37', '2025-38', '2025-39', '2025-40', '2025-41', '2025-42', '2025-43', '2025-44', '2025-45', '2025-46', '2025-47', '2025-48', '2025-49', '2025-50', '2025-51', '2025-52', '2026-01', '2026-02', '2026-03', '2026-04', '2026-05', '2026-06', '2026-07', '2026-08', '2026-09', '2026-10', '2026-11', '2026-12', '2026-13', '2026-14', '2026-15', '2026-16', '2026-17']
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Variable Periods - Forecast Periods over which we can control order placement
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - ------------------------------------------------------------------------------
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - ['2025-27', '2025-28', '2025-29', '2025-30', '2025-31', '2025-32', '2025-33', '2025-34', '2025-35', '2025-36', '2025-37', '2025-38', '2025-39', '2025-40', '2025-41', '2025-42', '2025-43', '2025-44', '2025-45', '2025-46', '2025-47', '2025-48', '2025-49', '2025-50', '2025-51', '2025-52', '2026-01', '2026-02', '2026-03', '2026-04', '2026-05', '2026-06', '2026-07', '2026-08', '2026-09', '2026-10', '2026-11', '2026-12', '2026-13', '2026-14', '2026-15', '2026-16', '2026-17']
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Beginning Period Inventory
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - --------------------------
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - {(2326059, '2025-19'): 185.0, (2404655, '2025-19'): 152.0, (2326058, '2025-19'): 85.0, (2326060, '2025-19'): 73.0, (2404656, '2025-19'): 56.0}
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Arrival Data 
 ---------- 
   {(2326058, '2025-31'): 77, (2326059, '2025-31'): 227, (2326060, '2025-31'): 61, (2404655, '2025-19'): 0, (2404655, '2025-20'): 0, (2404655, '2025-21'): 0, (2404655, '2025-22'): 0, (2404655, '2025-23'): 0, (2404655, '2025-24'): 0, (2404655, '2025-25'): 0, (2404655, '2025-26'): 0, (2404655, '2025-27'): 0, (2404655, '2025-28'): 0, (2404655, '2025-29'): 0, (2404655, '2025-30'): 0, (2404655, '2025-31'): 0, (2404655, '2025-32'): 0, (2404655, '2025-33'): 0, (2404655, '2025-34'): 0, (2404655, '2025-35'): 0, (2404655, '2025-36'): 0, (2404655, '2025-37'): 0, (2404655, '2025-38'): 0, (2404655, '2025-39'): 0, (2404655, '2025-40'): 0, (2404655, '2025-41'): 0, (2404655, '2025-42'): 0, (2404655, '2025-43'): 0, (2404655, '2025-44'): 0, (2404655, '2025-45'): 0, (2404655, '2025-46'): 0, (2404655, '2025-47'): 0, (2404655, '2025-48'): 0, (2404655, '2025-49'): 0, (2404655, '2025-50'): 0, (2404655, '2025-51'): 0, (2404655, '2025-52'): 0, (2404655, '2026-01'): 0, (2404655, '2026-02'): 0, (2404655, '2026-03'): 0, (2404655, '2026-04'): 0, (2404655, '2026-05'): 0, (2404655, '2026-06'): 0, (2404655, '2026-07'): 0, (2404655, '2026-08'): 0, (2404655, '2026-09'): 0, (2404655, '2026-10'): 0, (2404655, '2026-11'): 0, (2404655, '2026-12'): 0, (2404655, '2026-13'): 0, (2404655, '2026-14'): 0, (2404655, '2026-15'): 0, (2404655, '2026-16'): 0, (2404655, '2026-17'): 0, (2404656, '2025-19'): 0, (2404656, '2025-20'): 0, (2404656, '2025-21'): 0, (2404656, '2025-22'): 0, (2404656, '2025-23'): 0, (2404656, '2025-24'): 0, (2404656, '2025-25'): 0, (2404656, '2025-26'): 0, (2404656, '2025-27'): 0, (2404656, '2025-28'): 0, (2404656, '2025-29'): 0, (2404656, '2025-30'): 0, (2404656, '2025-31'): 0, (2404656, '2025-32'): 0, (2404656, '2025-33'): 0, (2404656, '2025-34'): 0, (2404656, '2025-35'): 0, (2404656, '2025-36'): 0, (2404656, '2025-37'): 0, (2404656, '2025-38'): 0, (2404656, '2025-39'): 0, (2404656, '2025-40'): 0, (2404656, '2025-41'): 0, (2404656, '2025-42'): 0, (2404656, '2025-43'): 0, (2404656, '2025-44'): 0, (2404656, '2025-45'): 0, (2404656, '2025-46'): 0, (2404656, '2025-47'): 0, (2404656, '2025-48'): 0, (2404656, '2025-49'): 0, (2404656, '2025-50'): 0, (2404656, '2025-51'): 0, (2404656, '2025-52'): 0, (2404656, '2026-01'): 0, (2404656, '2026-02'): 0, (2404656, '2026-03'): 0, (2404656, '2026-04'): 0, (2404656, '2026-05'): 0, (2404656, '2026-06'): 0, (2404656, '2026-07'): 0, (2404656, '2026-08'): 0, (2404656, '2026-09'): 0, (2404656, '2026-10'): 0, (2404656, '2026-11'): 0, (2404656, '2026-12'): 0, (2404656, '2026-13'): 0, (2404656, '2026-14'): 0, (2404656, '2026-15'): 0, (2404656, '2026-16'): 0, (2404656, '2026-17'): 0, (2326058, '2025-19'): 0, (2326058, '2025-20'): 0, (2326058, '2025-21'): 0, (2326058, '2025-22'): 0, (2326058, '2025-23'): 0, (2326058, '2025-24'): 0, (2326058, '2025-25'): 0, (2326058, '2025-26'): 0, (2326058, '2025-27'): 0, (2326058, '2025-28'): 0, (2326058, '2025-29'): 0, (2326058, '2025-30'): 0, (2326058, '2025-32'): 0, (2326058, '2025-33'): 0, (2326058, '2025-34'): 0, (2326058, '2025-35'): 0, (2326058, '2025-36'): 0, (2326058, '2025-37'): 0, (2326058, '2025-38'): 0, (2326058, '2025-39'): 0, (2326058, '2025-40'): 0, (2326058, '2025-41'): 0, (2326058, '2025-42'): 0, (2326058, '2025-43'): 0, (2326058, '2025-44'): 0, (2326058, '2025-45'): 0, (2326058, '2025-46'): 0, (2326058, '2025-47'): 0, (2326058, '2025-48'): 0, (2326058, '2025-49'): 0, (2326058, '2025-50'): 0, (2326058, '2025-51'): 0, (2326058, '2025-52'): 0, (2326058, '2026-01'): 0, (2326058, '2026-02'): 0, (2326058, '2026-03'): 0, (2326058, '2026-04'): 0, (2326058, '2026-05'): 0, (2326058, '2026-06'): 0, (2326058, '2026-07'): 0, (2326058, '2026-08'): 0, (2326058, '2026-09'): 0, (2326058, '2026-10'): 0, (2326058, '2026-11'): 0, (2326058, '2026-12'): 0, (2326058, '2026-13'): 0, (2326058, '2026-14'): 0, (2326058, '2026-15'): 0, (2326058, '2026-16'): 0, (2326058, '2026-17'): 0, (2326059, '2025-19'): 0, (2326059, '2025-20'): 0, (2326059, '2025-21'): 0, (2326059, '2025-22'): 0, (2326059, '2025-23'): 0, (2326059, '2025-24'): 0, (2326059, '2025-25'): 0, (2326059, '2025-26'): 0, (2326059, '2025-27'): 0, (2326059, '2025-28'): 0, (2326059, '2025-29'): 0, (2326059, '2025-30'): 0, (2326059, '2025-32'): 0, (2326059, '2025-33'): 0, (2326059, '2025-34'): 0, (2326059, '2025-35'): 0, (2326059, '2025-36'): 0, (2326059, '2025-37'): 0, (2326059, '2025-38'): 0, (2326059, '2025-39'): 0, (2326059, '2025-40'): 0, (2326059, '2025-41'): 0, (2326059, '2025-42'): 0, (2326059, '2025-43'): 0, (2326059, '2025-44'): 0, (2326059, '2025-45'): 0, (2326059, '2025-46'): 0, (2326059, '2025-47'): 0, (2326059, '2025-48'): 0, (2326059, '2025-49'): 0, (2326059, '2025-50'): 0, (2326059, '2025-51'): 0, (2326059, '2025-52'): 0, (2326059, '2026-01'): 0, (2326059, '2026-02'): 0, (2326059, '2026-03'): 0, (2326059, '2026-04'): 0, (2326059, '2026-05'): 0, (2326059, '2026-06'): 0, (2326059, '2026-07'): 0, (2326059, '2026-08'): 0, (2326059, '2026-09'): 0, (2326059, '2026-10'): 0, (2326059, '2026-11'): 0, (2326059, '2026-12'): 0, (2326059, '2026-13'): 0, (2326059, '2026-14'): 0, (2326059, '2026-15'): 0, (2326059, '2026-16'): 0, (2326059, '2026-17'): 0, (2326060, '2025-19'): 0, (2326060, '2025-20'): 0, (2326060, '2025-21'): 0, (2326060, '2025-22'): 0, (2326060, '2025-23'): 0, (2326060, '2025-24'): 0, (2326060, '2025-25'): 0, (2326060, '2025-26'): 0, (2326060, '2025-27'): 0, (2326060, '2025-28'): 0, (2326060, '2025-29'): 0, (2326060, '2025-30'): 0, (2326060, '2025-32'): 0, (2326060, '2025-33'): 0, (2326060, '2025-34'): 0, (2326060, '2025-35'): 0, (2326060, '2025-36'): 0, (2326060, '2025-37'): 0, (2326060, '2025-38'): 0, (2326060, '2025-39'): 0, (2326060, '2025-40'): 0, (2326060, '2025-41'): 0, (2326060, '2025-42'): 0, (2326060, '2025-43'): 0, (2326060, '2025-44'): 0, (2326060, '2025-45'): 0, (2326060, '2025-46'): 0, (2326060, '2025-47'): 0, (2326060, '2025-48'): 0, (2326060, '2025-49'): 0, (2326060, '2025-50'): 0, (2326060, '2025-51'): 0, (2326060, '2025-52'): 0, (2326060, '2026-01'): 0, (2326060, '2026-02'): 0, (2326060, '2026-03'): 0, (2326060, '2026-04'): 0, (2326060, '2026-05'): 0, (2326060, '2026-06'): 0, (2326060, '2026-07'): 0, (2326060, '2026-08'): 0, (2326060, '2026-09'): 0, (2326060, '2026-10'): 0, (2326060, '2026-11'): 0, (2326060, '2026-12'): 0, (2326060, '2026-13'): 0, (2326060, '2026-14'): 0, (2326060, '2026-15'): 0, (2326060, '2026-16'): 0, (2326060, '2026-17'): 0}
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Gross Margins Calculated
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - --------------------------
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - {2326059: '71.3900', 2404655: '84.9500', 2326058: '51.7400', 2326060: '88.6400', 2404656: '63.5900'}
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO -  Storage Cost Per Sq. Ft = $ 0.007 
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Storage Volume Calculated off the Ending Inventory
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - --------------------------------------------------
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - storage_volume
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Filtered Product Dimensions and Load Quantity
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - -----------------------------------------------
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO -      itemid        sku  ... load quantity unique identifier - for container product mix
46  2404655   15205-02  ...           300                                            55
47  2404656   15205-03  ...           413                                            55
48  2326058  05307-001  ...           523                                            55
49  2326059  05307-002  ...           300                                            55
50  2326060  05307-003  ...           413                                            55

[5 rows x 11 columns]
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Container Batch Size - Calculated as the mean of the load quantity for the selected items
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - -----------------------------------------------
2025-07-01 12:19:52 PM India Standard Time - main.py - INFO - Container Batch Size = 390.0
2025-07-01 12:30:16 PM India Standard Time - main.py - INFO - Optimization Results
2025-07-01 12:30:16 PM India Standard Time - main.py - INFO - ---------------------
2025-07-01 12:30:16 PM India Standard Time - main.py - INFO - week                      2025-19  2025-20  2025-21  2025-22  ...  2026-14       2026-15  2026-16       2026-17
type             itemid                                       ...                                              
forecast         2404655     15.0     15.0     15.0     15.0  ...     15.0  1.500000e+01     15.0  1.500000e+01
                 2404656      6.0      6.0      6.0      6.0  ...      6.0  6.000000e+00      6.0  6.000000e+00
                 2326058      9.0      9.0      9.0      9.0  ...      9.0  9.000000e+00      9.0  9.000000e+00
                 2326059     34.0     34.0     34.0     34.0  ...     34.0  3.400000e+01     34.0  3.400000e+01
                 2326060     10.0     10.0     10.0     10.0  ...     10.0  1.000000e+01     10.0  1.000000e+01
arrivals         2404655      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  0.000000e+00
                 2404656      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  0.000000e+00
                 2326058      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  0.000000e+00
                 2326059      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  0.000000e+00
                 2326060      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  0.000000e+00
shifted_arrivals 2404655      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  0.000000e+00
                 2404656      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  0.000000e+00
                 2326058      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  7.700000e+01
                 2326059      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  2.270000e+02
                 2326060      0.0      0.0      0.0      0.0  ...      0.0  0.000000e+00      0.0  6.100000e+01
orders_to_place  2404655      NaN      NaN      NaN      NaN  ...      0.0 -3.145746e-11     30.0  0.000000e+00
                 2404656      NaN      NaN      NaN      NaN  ...      0.0  0.000000e+00     12.0  0.000000e+00
                 2326058      NaN      NaN      NaN      NaN  ...      0.0  0.000000e+00    304.0  0.000000e+00
                 2326059      NaN      NaN      NaN      NaN  ...      0.0  0.000000e+00     34.0 -2.953304e-12
                 2326060      NaN      NaN      NaN      NaN  ...      0.0  0.000000e+00     10.0  1.500838e-12
ending_inv       2404655    137.0    122.0    107.0     92.0  ...     16.0  1.000000e+00     16.0  1.000000e+00
                 2404656     50.0     44.0     38.0     32.0  ...      7.0  1.000000e+00      7.0  1.000000e+00
                 2326058     76.0     67.0     58.0     49.0  ...     40.0  3.100000e+01    326.0  3.940000e+02
                 2326059    151.0    117.0     83.0     49.0  ...     35.0  1.000000e+00      1.0  1.940000e+02
                 2326060     63.0     53.0     43.0     33.0  ...     11.0  1.000000e+00      1.0  5.200000e+01

[25 rows x 51 columns]
